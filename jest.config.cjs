/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest/presets/default-esm', // Rely on the preset for transformations
  testEnvironment: 'node',
  extensionsToTreatAsEsm: ['.ts'],
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  // Remove explicit transform block, let the preset handle it
  // transform: {
  //   '^.+\\.tsx?$': [
  //     'ts-jest',
  //     {
  //       useESM: true,
  //     },
  //   ],
  // },
  // Explicitly ignore all node_modules from transformation
  transformIgnorePatterns: [
     '/node_modules/',
  ],
  moduleFileExtensions: ['ts', 'js', 'json', 'node'],
};
