import { chromium } from "playwright";
import { writeFileSync } from "fs";
import { URL } from 'url'; // Import URL for resolving relative paths

interface State {
  code: string;
  rulesPage: string;    // homepage or rules index
  linkTextHint: string; // text to look for e.g. "Rules of Civil Procedure"
}

// IMPORTANT: This array was auto-populated based on common search patterns.
// **Please review carefully and correct any inaccurate URLs or linkTextHints.**
// Court websites change, and automated discovery may not capture all nuances.
const states: State[] = [
  { code: "AL", rulesPage: "https://judicial.alabama.gov/library/rules", linkTextHint: "Rules of Civil Procedure" },
  { code: "AK", rulesPage: "https://courts.alaska.gov/rules/rules.htm", linkTextHint: "Civil Rules" }, // Adjusted page, hint seems okay
  { code: "AZ", rulesPage: "https://www.azcourts.gov/rules", linkTextHint: "Rules of Civil Procedure" },
  { code: "AR", rulesPage: "https://courts.arkansas.gov/rules-and-administrative-orders/rules-of-the-court", linkTextHint: "Rules of Civil Procedure" }, // Adjusted page and hint
  { code: "CA", rulesPage: "https://www.courts.ca.gov/rules.htm", linkTextHint: "California Rules of Court" },
  { code: "CO", rulesPage: "https://www.courts.state.co.us/Courts/Supreme_Court/Rule_Changes/Index.cfm", linkTextHint: "Colorado Rules of Civil Procedure" }, // Often under Rule Changes
  { code: "CT", rulesPage: "https://jud.ct.gov/publications/Practice_Book/index.htm", linkTextHint: "Rules of Professional Conduct" }, // Practice Book often contains civil rules
  { code: "DE", rulesPage: "https://courts.delaware.gov/rules/", linkTextHint: "Superior Court Rules of Civil Procedure" },
  // Updated FL to direct PDF link
  { code: "FL", rulesPage: "https://www-media.floridabar.org/uploads/2025/01/Civil-Procedure-Rules-01-01-25-Corrected-Opinion.pdf", linkTextHint: "Civil Procedure Rules" },
  { code: "GA", rulesPage: "https://www.gasupreme.us/rules/", linkTextHint: "RULES OF THE SUPREME COURT OF GEORGIA" }, // May need refinement, check specific civil rules link
  { code: "HI", rulesPage: "https://www.courts.state.hi.us/rules_and_orders/rules_of_court", linkTextHint: "Hawaiʻi Rules of Civil Procedure" },
  { code: "ID", rulesPage: "https://isc.idaho.gov/ircp", linkTextHint: "Idaho Rules of Civil Procedure" },
  { code: "IL", rulesPage: "https://www.illinoiscourts.gov/rules-and-standards/supreme-court-rules", linkTextHint: "Illinois Supreme Court Rules" }, // Civil procedure rules are within these
  { code: "IN", rulesPage: "https://www.in.gov/courts/rules/", linkTextHint: "Rules of Trial Procedure" }, // Indiana uses Trial Procedure
  { code: "IA", rulesPage: "https://www.iowacourts.gov/for-attorneys/practice-rules-and-forms/iowa-court-rules/", linkTextHint: "Iowa Rules of Civil Procedure" },
  { code: "KS", rulesPage: "https://www.kscourts.org/rules-procedures-forms/Rules-Procedures-Forms/Rules", linkTextHint: "Rules Relating to Civil Procedure" }, // Chapter 60 usually
  { code: "KY", rulesPage: "https://courts.ky.gov/resources/rulesandprocedures/Pages/default.aspx", linkTextHint: "Rules of Civil Procedure" },
  { code: "LA", rulesPage: "https://www.lasc.org/rules/", linkTextHint: "Code of Civil Procedure" }, // Louisiana uses Code
  { code: "ME", rulesPage: "https://www.courts.maine.gov/rules/index.html", linkTextHint: "Maine Rules of Civil Procedure" },
  { code: "MD", rulesPage: "https://mdcourts.gov/rules", linkTextHint: "Maryland Rules of Procedure" }, // Title 2 for Civil
  { code: "MA", rulesPage: "https://www.mass.gov/topics/court-rules", linkTextHint: "Massachusetts Rules of Civil Procedure" },
  { code: "MI", rulesPage: "https://courts.michigan.gov/administration/standards-guidelines/court-rules/", linkTextHint: "Michigan Court Rules" }, // Chapter 2 for Civil
  { code: "MN", rulesPage: "https://www.revisor.mn.gov/court_rules/cp/", linkTextHint: "Rules of Civil Procedure" },
  { code: "MS", rulesPage: "https://courts.ms.gov/rules/msrulesofcourt/rules_of_civil_procedure.pdf", linkTextHint: "Mississippi Rules of Civil Procedure" }, // Direct PDF link often
  { code: "MO", rulesPage: "https://www.courts.mo.gov/page.jsp?id=677", linkTextHint: "Rules of Civil Procedure" }, // Often listed as Rules 41-102
  { code: "MT", rulesPage: "https://courts.mt.gov/rules/", linkTextHint: "Rules of Civil Procedure" },
  { code: "NE", rulesPage: "https://supremecourt.nebraska.gov/supreme-court-rules", linkTextHint: "Rules of Pleading in Civil Actions" },
  { code: "NV", rulesPage: "https://www.leg.state.nv.us/nrcp/", linkTextHint: "Nevada Rules of Civil Procedure" },
  { code: "NH", rulesPage: "https://www.courts.nh.gov/rules-orders", linkTextHint: "Superior Court Rules - Civil" },
  { code: "NJ", rulesPage: "https://www.njcourts.gov/rules", linkTextHint: "Rules Governing the Courts" }, // Part IV for Civil
  { code: "NM", rulesPage: "https://nmonesource.com/nmos/nmrca/en/nav_date.do", linkTextHint: "Rules of Civil Procedure for the District Courts" },
  // Updated NY to HTML source - NOTE: fetchRules.ts cannot process this currently!
  { code: "NY", rulesPage: "http://public.leginfo.state.ny.us/LAWSSEAF.cgi?QUERYTYPE=LAWS&QUERYDATA=@LLCVP&LIST=LAW&TARGET=VIEW", linkTextHint: "Civil Practice Law & Rules" },
  { code: "NC", rulesPage: "https://www.nccourts.gov/rules-and-forms", linkTextHint: "Rules of Civil Procedure" },
  { code: "ND", rulesPage: "https://www.ndcourts.gov/rules-administrative-orders/north-dakota-rules-of-court", linkTextHint: "North Dakota Rules of Civil Procedure" },
  { code: "OH", rulesPage: "https://www.supremecourt.ohio.gov/legal-resources/rules-of-practice/", linkTextHint: "Ohio Rules of Civil Procedure" },
  { code: "OK", rulesPage: "https://www.oscn.net/applications/oscn/index.asp?ftdb=STOKST12&level=1", linkTextHint: "TITLE 12 CIVIL PROCEDURE" },
  { code: "OR", rulesPage: "https://www.oregonlegislature.gov/bills_laws/pages/orcp.aspx", linkTextHint: "Oregon Rules of Civil Procedure" },
  { code: "PA", rulesPage: "https://www.pacodeandbulletin.gov/Display/pacode?p=0.0.0.0.0&d=reduce&t=231", linkTextHint: "Rules of Civil Procedure" },
  { code: "RI", rulesPage: "https://www.courts.ri.gov/rules/default.aspx", linkTextHint: "Superior Court Rules of Civil Procedure" },
  { code: "SC", rulesPage: "https://www.sccourts.org/courtReg/", linkTextHint: "South Carolina Rules of Civil Procedure" },
  { code: "SD", rulesPage: "https://ujs.sd.gov/rules/", linkTextHint: "Rules Of Civil Procedure" }, // Title 15
  { code: "TN", rulesPage: "https://www.tncourts.gov/rules-all", linkTextHint: "Tennessee Rules of Civil Procedure" },
  // Updated TX to direct PDF link
  { code: "TX", rulesPage: "https://www.txcourts.gov/media/1446498/trcp-all-updated-with-amendments-effective-may-1-2020.pdf", linkTextHint: "Texas Rules of Civil Procedure" },
  { code: "UT", rulesPage: "https://www.utcourts.gov/rules/", linkTextHint: "Utah Rules of Civil Procedure" },
  { code: "VT", rulesPage: "https://www.vermontjudiciary.org/rules-and-legislation", linkTextHint: "Vermont Rules of Civil Procedure" },
  { code: "VA", rulesPage: "https://www.vacourts.gov/courts/scv/rulesofcourt.pdf", linkTextHint: "Rules of Supreme Court of Virginia" }, // Often direct PDF, check Part Three
  { code: "WA", rulesPage: "https://www.courts.wa.gov/court_rules/", linkTextHint: "Superior Court Civil Rules" },
  { code: "WV", rulesPage: "https://www.courtswv.gov/legal-community/court-rules.html", linkTextHint: "Rules of Civil Procedure" },
  { code: "WI", rulesPage: "https://docs.legis.wisconsin.gov/statutes/statutes/801", linkTextHint: "Civil Procedure" }, // Chapters 801-847
  { code: "WY", rulesPage: "https://www.courts.state.wy.us/RULES/index.html", linkTextHint: "Rules of Civil Procedure" } // Adjusted hint slightly
];

async function discover() {
  console.log("Launching browser...");
  const browser = await chromium.launch();
  const results: Record<string,string> = {};
  const errors: Record<string, string> = {}; // Collect errors

  console.log(`Starting discovery for ${states.length} states...`);
  for (const { code, rulesPage, linkTextHint } of states) {
    let context = null; 
    let page = null; 
    console.log(`Processing ${code}...`);
    try {
      // Check if rulesPage is already a direct PDF link
      if (rulesPage.toLowerCase().endsWith('.pdf')) {
        console.log(`  Using direct PDF link: ${rulesPage}`);
        results[`${code}_STATE`] = rulesPage;
        continue; // Skip browser navigation and link finding for direct PDFs
      }

      // If not a direct PDF, proceed with browser automation
      context = await browser.newContext();
      page = await context.newPage();

      // Increase timeout to 120 seconds and use networkidle
      await page.goto(rulesPage, { timeout: 120000, waitUntil: 'networkidle' });
      console.log(`  Navigated to ${rulesPage}`);

      // --- Link Finding Logic --- 
      let href: string | null = null;

      // 1. Try to find a direct PDF link containing 'civil' (case-insensitive)
      const pdfCivilLocator = page.locator('a[href*=".pdf"][href*="civil" i]');
      const pdfCivilCount = await pdfCivilLocator.count();
      if (pdfCivilCount > 0) {
        href = await pdfCivilLocator.first().getAttribute('href');
        console.log(`  Found direct PDF link via href [civil]: ${href}`);
      }

      // 2. If no luck, try finding any PDF link
      if (!href) {
        const pdfLocator = page.locator('a[href*=".pdf"]');
        const pdfCount = await pdfLocator.count();
        if (pdfCount > 0) {
          href = await pdfLocator.first().getAttribute('href');
          console.log(`  Found direct PDF link via href [.pdf]: ${href}`);
        }
      }

      // 3. If still no luck, fall back to the linkTextHint
      if (!href) {
        const textLocator = page.locator(`a:has-text("${linkTextHint}")`);
        const textCount = await textLocator.count();
        if (textCount > 0) {
          href = await textLocator.first().getAttribute("href");
          console.log(`  Found link via text hint "${linkTextHint}": ${href}`);
        } else {
          console.warn(`  ${code}: Link hint "${linkTextHint}" not found AND no suitable PDF link found on ${rulesPage}`);
          errors[code] = `Link hint "${linkTextHint}" not found and no direct PDF link found.`;
          continue; // Skip to next state if hint not found
        }
      }
      // --- End Link Finding Logic --- 

      if (!href) { // Should be unlikely now, but check
        console.warn(`  ${code}: Could not extract href for "${linkTextHint}"`);
        continue;
      }

      const absoluteUrl = new URL(href, page.url()).toString();
      results[`${code}_STATE`] = absoluteUrl;
      console.log(`  ${code}: Found URL -> ${absoluteUrl}`);

    } catch (e: any) {
      console.error(`  ${code}: Error fetching or processing ${rulesPage}: ${e.message}`);
      // Record error
      errors[code] = e.message.split('\n')[0]; // Store first line of error
    } finally {
       // Ensure context and page are closed even if errors occur
       // Check if page exists before trying to close (it might not if it was a direct PDF)
       if (page) {
         await page.close();
       }
       // Check if context exists before trying to close (it might not if it was a direct PDF)
       if (context) {
         await context.close();
       }
    }
  }

  console.log("Closing browser...");
  await browser.close();

  const outputPath = "scripts/sources.json";
  try {
    writeFileSync(outputPath, JSON.stringify(results, null, 2));
    console.log(`\nSuccessfully wrote ${outputPath} with ${Object.keys(results).length} entries.`);
  } catch (writeError: any) {
     console.error(`Error writing ${outputPath}: ${writeError.message}`);
  }

  // Report errors
  const errorCount = Object.keys(errors).length;
  if (errorCount > 0) {
    console.error(`\nEncountered errors for ${errorCount} state(s):`);
    for (const [code, message] of Object.entries(errors)) {
        console.error(`  - ${code}: ${message.split('\n')[0]}`); // Show first line of error
    }
    console.log("\nPlease review the 'states' array in discoverRules.ts for the states listed above.");
  }
}

discover().catch(err => {
  console.error("Unhandled error during discovery process:", err);
  process.exit(1);
});
