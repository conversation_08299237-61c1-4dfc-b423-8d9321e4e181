#!/usr/bin/env python3
"""
Trigger Clarity Analysis - Check for explicit trigger_event fields and clarity
"""

import yaml
import random
import re
import os
from datetime import datetime, timedelta

def load_sample_rules(sample_size=50):
    """Load sample rules from all categories"""
    rule_files = [
        "rules/Florida/processed/civil_deadlines_gemini_2_0_flash.yaml",
        "rules/Florida/processed/criminal_deadlines_gemini.yaml", 
        "rules/Florida/processed/family_deadlines_gemini.yaml",
        "rules/Texas/processed/civil_deadlines_gemini.yaml",
        "rules/Texas/processed/criminal_deadlines_gemini.yaml",
        "rules/Texas/processed/family_deadlines_gemini.yaml"
    ]
    
    all_rules = []
    
    for file_path in rule_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                
            jurisdiction = data.get('jurisdiction', 'Unknown')
            practice_area = data.get('practice_area', 'Unknown')
            
            for category_name, category_data in data.get('deadline_categories', {}).items():
                for rule_key, rule_data in category_data.get('rules', {}).items():
                    rule_info = {
                        'jurisdiction': jurisdiction,
                        'practice_area': practice_area,
                        'category': category_name,
                        'rule_key': rule_key,
                        'rule': rule_data.get('rule', ''),
                        'deadline': rule_data.get('deadline', ''),
                        'description': rule_data.get('description', ''),
                        'trigger': rule_data.get('trigger', ''),
                        'consequence': rule_data.get('consequence', ''),
                        'source_file': file_path
                    }
                    all_rules.append(rule_info)
    
    # Return random sample
    return random.sample(all_rules, min(sample_size, len(all_rules)))

def analyze_trigger_clarity(rule):
    """Analyze trigger clarity and suggest improvements"""
    trigger = rule['trigger'].strip()
    
    # Check for vague triggers
    vague_patterns = [
        r'^n/a$',
        r'^not specified$',
        r'^none$',
        r'^unclear',
        r'^various',
        r'^depends',
        r'^when'
    ]
    
    is_vague = any(re.search(pattern, trigger.lower()) for pattern in vague_patterns)
    
    # Check for specific event types
    specific_events = {
        'filing': r'fil(e|ing)',
        'service': r'serv(e|ice|ed)',
        'arrest': r'arrest',
        'conviction': r'convict',
        'hearing': r'hearing',
        'order': r'order.*enter|enter.*order',
        'notice': r'notice.*receiv|receiv.*notice',
        'motion': r'motion.*fil|fil.*motion',
        'judgment': r'judgment.*enter|enter.*judgment'
    }
    
    detected_events = []
    for event_type, pattern in specific_events.items():
        if re.search(pattern, trigger.lower()):
            detected_events.append(event_type)
    
    # Suggest structured trigger_event field
    suggested_trigger_event = None
    if 'filing' in detected_events:
        suggested_trigger_event = 'documentFilingDatetime'
    elif 'service' in detected_events:
        suggested_trigger_event = 'serviceCompletionDatetime'
    elif 'arrest' in detected_events:
        suggested_trigger_event = 'arrestBookingDatetime'
    elif 'hearing' in detected_events:
        suggested_trigger_event = 'hearingScheduledDatetime'
    elif 'order' in detected_events:
        suggested_trigger_event = 'orderEntryDatetime'
    elif 'notice' in detected_events:
        suggested_trigger_event = 'noticeReceiptDatetime'
    elif 'judgment' in detected_events:
        suggested_trigger_event = 'judgmentEntryDatetime'
    
    return {
        'is_vague': is_vague,
        'detected_events': detected_events,
        'suggested_trigger_event': suggested_trigger_event,
        'clarity_score': len(detected_events) if not is_vague else 0
    }

def test_holiday_edge_cases():
    """Test holiday edge cases with sample dates"""
    print(f"\n🗓️  HOLIDAY EDGE CASE TESTING")
    print(f"=" * 40)
    
    # Sample dates that cross holidays
    test_dates = [
        ('2024-12-23', '5 days'),  # Christmas
        ('2024-12-30', '3 days'),  # New Year
        ('2024-07-03', '2 days'),  # July 4th
        ('2024-11-27', '4 days'),  # Thanksgiving
        ('2024-02-16', '3 days'),  # Presidents Day
        ('2024-05-24', '5 days'),  # Memorial Day
        ('2024-09-02', '1 day'),   # Labor Day
        ('2024-10-11', '3 days'),  # Columbus Day
        ('2024-11-08', '5 days'),  # Veterans Day
        ('2024-01-12', '3 days')   # MLK Day
    ]
    
    print(f"Testing deadline calculations across holidays:")
    print(f"(Note: This is a simulation - actual implementation needed)")
    
    for start_date_str, deadline_str in test_dates:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        days = int(deadline_str.split()[0])
        
        # Simple business day calculation (excluding weekends)
        current_date = start_date
        business_days_added = 0
        
        while business_days_added < days:
            current_date += timedelta(days=1)
            # Skip weekends (basic implementation)
            if current_date.weekday() < 5:  # Monday = 0, Friday = 4
                business_days_added += 1
        
        print(f"  {start_date_str} + {deadline_str} → {current_date.strftime('%Y-%m-%d')} ({current_date.strftime('%A')})")
    
    print(f"\n⚠️  Recommendations:")
    print(f"  - Implement proper federal/state holiday calendar")
    print(f"  - Add roll-forward logic for weekends/holidays")
    print(f"  - Test with jurisdiction-specific court calendars")

def analyze_version_stamps():
    """Check for version stamps in extracted files"""
    print(f"\n📋 VERSION STAMP ANALYSIS")
    print(f"=" * 40)
    
    rule_files = [
        "rules/Florida/processed/civil_deadlines_gemini_2_0_flash.yaml",
        "rules/Florida/processed/criminal_deadlines_gemini.yaml", 
        "rules/Florida/processed/family_deadlines_gemini.yaml",
        "rules/Texas/processed/civil_deadlines_gemini.yaml",
        "rules/Texas/processed/criminal_deadlines_gemini.yaml",
        "rules/Texas/processed/family_deadlines_gemini.yaml"
    ]
    
    for file_path in rule_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            last_updated = data.get('last_updated', 'Not specified')
            extraction_date = data.get('extraction_date', 'Not specified')
            source = data.get('source', 'Not specified')
            
            print(f"\n{file_path}:")
            print(f"  Source: {source}")
            print(f"  Last Updated: {last_updated}")
            print(f"  Extraction Date: {extraction_date}")
            
            # Check if we need source_version field
            if 'source_version' not in data:
                print(f"  ⚠️  Missing source_version field")
            else:
                print(f"  ✅ Source Version: {data['source_version']}")

if __name__ == "__main__":
    print("🔍 Starting Trigger Clarity Analysis...")
    
    # Set random seed for reproducible results
    random.seed(42)
    
    # Load sample rules
    sample_rules = load_sample_rules(20)
    
    print(f"📊 TRIGGER CLARITY ANALYSIS")
    print(f"=" * 50)
    print(f"Analyzing {len(sample_rules)} sample rules...")
    
    vague_count = 0
    clear_count = 0
    
    for i, rule in enumerate(sample_rules, 1):
        analysis = analyze_trigger_clarity(rule)
        
        status = "❌" if analysis['is_vague'] else "✅" if analysis['clarity_score'] > 0 else "⚠️"
        
        print(f"\n{i}. {status} {rule['jurisdiction']} {rule['practice_area']} - {rule['rule']}")
        print(f"   Trigger: {rule['trigger'][:80]}...")
        print(f"   Detected events: {', '.join(analysis['detected_events']) if analysis['detected_events'] else 'None'}")
        
        if analysis['suggested_trigger_event']:
            print(f"   Suggested trigger_event: {analysis['suggested_trigger_event']}")
        
        if analysis['is_vague']:
            vague_count += 1
        elif analysis['clarity_score'] > 0:
            clear_count += 1
    
    # Summary
    vague_percentage = (vague_count / len(sample_rules)) * 100
    clear_percentage = (clear_count / len(sample_rules)) * 100
    
    print(f"\n📈 TRIGGER CLARITY METRICS:")
    print(f"Vague triggers: {vague_count}/{len(sample_rules)} ({vague_percentage:.1f}%)")
    print(f"Clear triggers: {clear_count}/{len(sample_rules)} ({clear_percentage:.1f}%)")
    
    # Test holiday edge cases
    test_holiday_edge_cases()
    
    # Analyze version stamps
    analyze_version_stamps()
    
    print(f"\n✅ Analysis complete!")
    print(f"\nRecommendations:")
    print(f"1. Add explicit 'trigger_event' field to all rules")
    print(f"2. Standardize trigger event naming (e.g., 'arrestBookingDatetime')")
    print(f"3. Implement holiday-aware deadline calculation")
    print(f"4. Add 'source_version' field with effective dates")
