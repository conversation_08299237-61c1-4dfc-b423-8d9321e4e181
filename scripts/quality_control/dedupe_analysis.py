#!/usr/bin/env python3
"""
Deduplication Analysis for Legal Deadline Rules
Checks for duplicate deadlines across all extracted rules using (trigger, deadline, consequence) tuples
"""

import yaml
import hashlib
import json
from collections import defaultdict, Counter
import os

def load_all_rules():
    """Load all extracted rules from both jurisdictions"""
    rule_files = [
        "rules/Florida/processed/civil_deadlines_gemini_2_0_flash.yaml",
        "rules/Florida/processed/criminal_deadlines_gemini.yaml", 
        "rules/Florida/processed/family_deadlines_gemini.yaml",
        "rules/Texas/processed/civil_deadlines_gemini.yaml",
        "rules/Texas/processed/criminal_deadlines_gemini.yaml",
        "rules/Texas/processed/family_deadlines_gemini.yaml"
    ]
    
    all_rules = []
    
    for file_path in rule_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                
            jurisdiction = data.get('jurisdiction', 'Unknown')
            practice_area = data.get('practice_area', 'Unknown')
            
            for category_name, category_data in data.get('deadline_categories', {}).items():
                for rule_key, rule_data in category_data.get('rules', {}).items():
                    rule_info = {
                        'jurisdiction': jurisdiction,
                        'practice_area': practice_area,
                        'category': category_name,
                        'rule_key': rule_key,
                        'rule': rule_data.get('rule', ''),
                        'deadline': rule_data.get('deadline', ''),
                        'description': rule_data.get('description', ''),
                        'trigger': rule_data.get('trigger', ''),
                        'consequence': rule_data.get('consequence', ''),
                        'extensions': rule_data.get('extensions', ''),
                        'source_file': file_path
                    }
                    all_rules.append(rule_info)
        else:
            print(f"Warning: File not found: {file_path}")
    
    return all_rules

def create_rule_hash(rule):
    """Create hash from (trigger, deadline, consequence) tuple"""
    # Normalize text for comparison
    trigger = rule['trigger'].lower().strip()
    deadline = rule['deadline'].lower().strip()
    consequence = rule['consequence'].lower().strip()
    
    # Create tuple for hashing
    hash_tuple = (trigger, deadline, consequence)
    hash_string = json.dumps(hash_tuple, sort_keys=True)
    
    return hashlib.md5(hash_string.encode()).hexdigest()

def analyze_duplicates(rules):
    """Analyze duplicate rules"""
    hash_to_rules = defaultdict(list)
    
    # Group rules by hash
    for rule in rules:
        rule_hash = create_rule_hash(rule)
        hash_to_rules[rule_hash].append(rule)
    
    # Find duplicates
    duplicates = {h: rules_list for h, rules_list in hash_to_rules.items() if len(rules_list) > 1}
    
    print(f"📊 DEDUPLICATION ANALYSIS")
    print(f"=" * 50)
    print(f"Total rules analyzed: {len(rules)}")
    print(f"Unique rule hashes: {len(hash_to_rules)}")
    print(f"Duplicate groups found: {len(duplicates)}")
    
    if duplicates:
        total_duplicate_rules = sum(len(rules_list) - 1 for rules_list in duplicates.values())
        duplicate_percentage = (total_duplicate_rules / len(rules)) * 100
        print(f"Total duplicate rules: {total_duplicate_rules}")
        print(f"Duplication rate: {duplicate_percentage:.1f}%")
        
        print(f"\n🔍 SAMPLE DUPLICATES:")
        print(f"-" * 30)
        
        for i, (hash_val, duplicate_rules) in enumerate(list(duplicates.items())[:5]):
            print(f"\nDuplicate Group {i+1}:")
            print(f"Hash: {hash_val[:8]}...")
            print(f"Count: {len(duplicate_rules)} rules")
            
            for j, rule in enumerate(duplicate_rules):
                print(f"  {j+1}. {rule['jurisdiction']} {rule['practice_area']} - {rule['rule']}")
                print(f"     Trigger: {rule['trigger'][:60]}...")
                print(f"     Deadline: {rule['deadline']}")
                print(f"     Consequence: {rule['consequence'][:60]}...")
    else:
        print("✅ No duplicates found!")
    
    return duplicates

def analyze_category_distribution(rules):
    """Analyze category distribution to identify potential noise"""
    print(f"\n📈 CATEGORY DISTRIBUTION ANALYSIS")
    print(f"=" * 50)
    
    # Count by category across all jurisdictions
    category_counts = Counter()
    jurisdiction_practice_counts = Counter()
    
    for rule in rules:
        category_counts[rule['category']] += 1
        jurisdiction_practice_counts[(rule['jurisdiction'], rule['practice_area'])] += 1
    
    print(f"Top 10 categories by rule count:")
    for category, count in category_counts.most_common(10):
        percentage = (count / len(rules)) * 100
        print(f"  {category}: {count} rules ({percentage:.1f}%)")
    
    print(f"\nRules by jurisdiction and practice area:")
    for (jurisdiction, practice_area), count in jurisdiction_practice_counts.most_common():
        print(f"  {jurisdiction} {practice_area}: {count} rules")
    
    # Identify large "other" categories
    large_other_categories = [(cat, count) for cat, count in category_counts.items() 
                             if 'other' in cat.lower() and count > 50]
    
    if large_other_categories:
        print(f"\n⚠️  LARGE 'OTHER' CATEGORIES (potential noise):")
        for category, count in large_other_categories:
            print(f"  {category}: {count} rules")
    
    return category_counts

if __name__ == "__main__":
    print("🔍 Starting Quality Control Analysis...")
    
    # Load all rules
    all_rules = load_all_rules()
    
    if not all_rules:
        print("❌ No rules loaded. Check file paths.")
        exit(1)
    
    # Analyze duplicates
    duplicates = analyze_duplicates(all_rules)
    
    # Analyze category distribution
    category_counts = analyze_category_distribution(all_rules)
    
    print(f"\n✅ Analysis complete!")
    print(f"Next steps:")
    print(f"1. Review duplicate groups for actual duplicates vs. similar rules")
    print(f"2. Sample large 'other' categories for noise")
    print(f"3. Check trigger clarity in sample rules")
