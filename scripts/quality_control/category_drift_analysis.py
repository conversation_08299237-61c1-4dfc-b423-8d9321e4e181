#!/usr/bin/env python3
"""
Category Drift Analysis - Sample "other" category rules to identify noise
"""

import yaml
import random
import re
import os

def load_rules_by_category(category_name="other"):
    """Load all rules from specified category"""
    rule_files = [
        "rules/Florida/processed/civil_deadlines_gemini_2_0_flash.yaml",
        "rules/Florida/processed/criminal_deadlines_gemini.yaml", 
        "rules/Florida/processed/family_deadlines_gemini.yaml",
        "rules/Texas/processed/civil_deadlines_gemini.yaml",
        "rules/Texas/processed/criminal_deadlines_gemini.yaml",
        "rules/Texas/processed/family_deadlines_gemini.yaml"
    ]
    
    category_rules = []
    
    for file_path in rule_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                
            jurisdiction = data.get('jurisdiction', 'Unknown')
            practice_area = data.get('practice_area', 'Unknown')
            
            for cat_name, category_data in data.get('deadline_categories', {}).items():
                if cat_name.lower() == category_name.lower():
                    for rule_key, rule_data in category_data.get('rules', {}).items():
                        rule_info = {
                            'jurisdiction': jurisdiction,
                            'practice_area': practice_area,
                            'category': cat_name,
                            'rule_key': rule_key,
                            'rule': rule_data.get('rule', ''),
                            'deadline': rule_data.get('deadline', ''),
                            'description': rule_data.get('description', ''),
                            'trigger': rule_data.get('trigger', ''),
                            'consequence': rule_data.get('consequence', ''),
                            'source_file': file_path
                        }
                        category_rules.append(rule_info)
    
    return category_rules

def has_calendar_deadline(rule):
    """Check if rule has a clear calendar-based deadline"""
    deadline = rule['deadline'].lower()
    
    # Calendar-based deadline patterns
    calendar_patterns = [
        r'\d+\s*(days?|months?|years?|hours?|minutes?)',
        r'within\s+\d+',
        r'before\s+\d+',
        r'after\s+\d+',
        r'not\s+later\s+than',
        r'not\s+earlier\s+than',
        r'immediately',
        r'promptly',
        r'forthwith'
    ]
    
    for pattern in calendar_patterns:
        if re.search(pattern, deadline):
            return True
    
    return False

def has_clear_trigger(rule):
    """Check if rule has a clear triggering event"""
    trigger = rule['trigger'].lower()
    
    # Vague trigger patterns (noise indicators)
    vague_patterns = [
        r'^n/a$',
        r'^not specified$',
        r'^none$',
        r'^unclear',
        r'^enactment',
        r'^annual',
        r'^end of.*year$'
    ]
    
    for pattern in vague_patterns:
        if re.search(pattern, trigger):
            return False
    
    # Must have some substantive content
    return len(trigger.strip()) > 10

def analyze_category_quality(rules, sample_size=20):
    """Analyze quality of rules in category"""
    print(f"📊 CATEGORY QUALITY ANALYSIS")
    print(f"=" * 50)
    print(f"Total rules in category: {len(rules)}")
    
    # Sample rules for manual review
    sample_rules = random.sample(rules, min(sample_size, len(rules)))
    
    calendar_deadline_count = 0
    clear_trigger_count = 0
    
    print(f"\n🔍 SAMPLE ANALYSIS ({len(sample_rules)} rules):")
    print(f"-" * 40)
    
    for i, rule in enumerate(sample_rules, 1):
        has_calendar = has_calendar_deadline(rule)
        has_trigger = has_clear_trigger(rule)
        
        if has_calendar:
            calendar_deadline_count += 1
        if has_trigger:
            clear_trigger_count += 1
        
        quality_score = "✅" if (has_calendar and has_trigger) else "⚠️" if (has_calendar or has_trigger) else "❌"
        
        print(f"\n{i}. {quality_score} {rule['jurisdiction']} {rule['practice_area']} - {rule['rule']}")
        print(f"   Deadline: {rule['deadline']}")
        print(f"   Trigger: {rule['trigger'][:80]}...")
        print(f"   Description: {rule['description'][:80]}...")
        print(f"   Calendar deadline: {'Yes' if has_calendar else 'No'}")
        print(f"   Clear trigger: {'Yes' if has_trigger else 'No'}")
    
    # Summary statistics
    calendar_percentage = (calendar_deadline_count / len(sample_rules)) * 100
    trigger_percentage = (clear_trigger_count / len(sample_rules)) * 100
    
    print(f"\n📈 QUALITY METRICS:")
    print(f"Rules with calendar deadlines: {calendar_deadline_count}/{len(sample_rules)} ({calendar_percentage:.1f}%)")
    print(f"Rules with clear triggers: {clear_trigger_count}/{len(sample_rules)} ({trigger_percentage:.1f}%)")
    
    if calendar_percentage < 50:
        print(f"⚠️  WARNING: Low calendar deadline rate suggests noise in category")
    
    if trigger_percentage < 50:
        print(f"⚠️  WARNING: Low clear trigger rate suggests extraction issues")
    
    return {
        'total_rules': len(rules),
        'sample_size': len(sample_rules),
        'calendar_deadline_rate': calendar_percentage,
        'clear_trigger_rate': trigger_percentage
    }

if __name__ == "__main__":
    print("🔍 Starting Category Drift Analysis...")
    
    # Analyze "other" category
    other_rules = load_rules_by_category("other")
    
    if not other_rules:
        print("❌ No 'other' category rules found.")
        exit(1)
    
    # Set random seed for reproducible sampling
    random.seed(42)
    
    # Analyze quality
    results = analyze_category_quality(other_rules, sample_size=20)
    
    print(f"\n✅ Analysis complete!")
    print(f"\nRecommendations:")
    if results['calendar_deadline_rate'] < 50:
        print(f"- Tighten extraction prompt to focus on calendar-based deadlines")
        print(f"- Consider filtering out administrative/reporting rules")
    if results['clear_trigger_rate'] < 50:
        print(f"- Improve trigger event extraction in prompts")
        print(f"- Add validation for trigger clarity")
