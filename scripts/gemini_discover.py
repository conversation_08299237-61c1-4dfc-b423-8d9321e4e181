import os
import sys
import json
import requests
import io
from PyPDF2 import PdfReader
from dotenv import load_dotenv

# Load environment variables (for GEMINI_API_KEY)
load_dotenv()

# --- Configuration ---
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    print("Error: GEMINI_API_KEY not found in environment variables.", file=sys.stderr)
    sys.exit(1)

# Using a known stable model, adjust if needed
GEMINI_MODEL = 'gemini-1.5-flash-latest' 
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/{GEMINI_MODEL}:generateContent?key={GEMINI_API_KEY}"
MAX_TOC_PAGES = 20  # Max pages to scan for TOC
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), '../rules')
OUTPUT_TOC_FILE = os.path.join(OUTPUT_DIR, 'fl-toc.json')
# ---

def discover_toc(pdf_url):
    """Fetches PDF, scans for TOC using Gemini, saves to JSON."""
    if not pdf_url:
        print("Usage: python scripts/gemini_discover.py <PDF_URL>", file=sys.stderr)
        sys.exit(1)

    print(f"Fetching PDF from {pdf_url}...")
    try:
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
        response = requests.get(pdf_url, stream=True, headers=headers, timeout=60)
        response.raise_for_status() # Raise exception for bad status codes
        pdf_data = response.content
    except requests.exceptions.RequestException as e:
        print(f"Error fetching PDF: {e}", file=sys.stderr)
        sys.exit(1)

    print("PDF downloaded. Reading content...")
    try:
        pdf_file = io.BytesIO(pdf_data)
        reader = PdfReader(pdf_file)
        num_pages = len(reader.pages)
    except Exception as e:
        print(f"Error reading PDF: {e}", file=sys.stderr)
        sys.exit(1)

    print(f"PDF loaded ({num_pages} pages). Discovering TOC (scanning up to page {MAX_TOC_PAGES})...")

    raw_entries = [] # List of {'code': str, 'title': str, 'page': int}

    headers = {'Content-Type': 'application/json'}

    for i in range(min(MAX_TOC_PAGES, num_pages)):
        page_num = i + 1
        print(f"Scanning page {page_num}...", end=' ')
        try:
            page = reader.pages[i]
            text = page.extract_text()
            if not text or len(text.strip()) < 50: # Skip pages with very little text
                print("Skipped (minimal text)")
                continue

            prompt = (
                f"Extract table of contents entries for Florida Rules of Civil Procedure from the following page text. "
                f"Focus on entries starting with 'RULE' followed by a number (e.g., 'RULE 1.100'). "
                f"Return ONLY a valid JSON array of objects, where each object has 'code' (the rule number like '1.100') and 'title' keys. "
                f"If no rules are found on this page, return an empty JSON array []. "
                f"Do not include explanations or markdown formatting. Example: [{{'code': '1.100', 'title': 'PLEADINGS AND MOTIONS'}}]\n\n"
                f"Page Text:\n```\n{text}\n```"
            )

            data = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 1024,
                    "responseMimeType": "application/json",
                }
            }

            api_response = requests.post(GEMINI_API_URL, headers=headers, json=data, timeout=60)

            if api_response.status_code != 200:
                print(f"Error from Gemini API (Page {page_num}): {api_response.status_code} {api_response.text}")
                continue

            response_json = api_response.json()

            # Navigate the response structure for Gemini 1.5 Flash
            if not response_json.get('candidates') or not response_json['candidates'][0].get('content') or not response_json['candidates'][0]['content'].get('parts'):
                 print(f"Warning: Unexpected API response format on page {page_num}")
                 continue

            content_text = response_json['candidates'][0]['content']['parts'][0].get('text', '[]').strip()

            # Basic cleanup - Gemini might add markdown backticks
            if content_text.startswith('```json'):
                content_text = content_text[7:]
            if content_text.endswith('```'):
                content_text = content_text[:-3]
            content_text = content_text.strip()

            if not content_text:
                print("Skipped (empty response)")
                continue

            try:
                page_entries = json.loads(content_text)
                if isinstance(page_entries, list):
                    valid_entries = 0
                    for entry in page_entries:
                        if isinstance(entry, dict) and 'code' in entry and 'title' in entry:
                            raw_entries.append({'code': str(entry['code']), 'title': str(entry['title']), 'page': page_num})
                            valid_entries += 1
                    print(f"Found {valid_entries} entries.")
                else:
                    print(f"Warning: Gemini response for page {page_num} was not a JSON list.")
            except json.JSONDecodeError as json_err:
                print(f"Warning: Could not parse JSON from Gemini on page {page_num}: {json_err}")
                print(f"Raw response text: {content_text[:200]}...") # Log snippet of bad response

        except Exception as page_err:
            print(f"Error processing page {page_num}: {page_err}")

    # Process raw entries to get start/end pages
    toc_map = {}
    for entry in raw_entries:
        code = entry['code']
        if code not in toc_map:
            toc_map[code] = {'code': code, 'title': entry['title'], 'startPage': entry['page'], 'endPage': entry['page']}
        else:
            # Update end page if this entry is on a later page
            toc_map[code]['endPage'] = max(toc_map[code]['endPage'], entry['page'])
            # Optionally update start page if found earlier (though less likely with sequential scan)
            toc_map[code]['startPage'] = min(toc_map[code]['startPage'], entry['page'])
            # Keep the title from the first occurrence (usually less fragmented)

    def get_sort_key(entry):
        code = entry['code']
        parts = code.split('.')
        try:
            # Extract numeric parts, ignore trailing non-digits like '(a)'
            major = int(parts[0])
            minor_str = ''.join(filter(str.isdigit, parts[1])) # Get only digits from minor part
            minor = int(minor_str) if minor_str else 0
            # Create a comparable float or tuple
            return float(f"{major}.{minor:03d}") # Format minor with leading zeros for sorting
        except (ValueError, IndexError):
            print(f"Warning: Could not parse rule code '{code}' for sorting. Placing it at the end.")
            return float('inf') # Place unparsable codes at the end

    final_toc = sorted(list(toc_map.values()), key=get_sort_key)

    # Create output directory if it doesn't exist
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # Write TOC to JSON file
    try:
        with open(OUTPUT_TOC_FILE, 'w', encoding='utf-8') as f:
            json.dump(final_toc, f, indent=2, ensure_ascii=False)
        print(f"\nDiscovered {len(final_toc)} TOC entries. Written to {OUTPUT_TOC_FILE}")
    except IOError as e:
        print(f"Error writing TOC file: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    pdf_url_arg = sys.argv[1] if len(sys.argv) > 1 else None
    discover_toc(pdf_url_arg)
