import 'dotenv/config';
import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';
import pdfjsLib from 'pdfjs-dist';

async function parseFloridaRulesWithGemini(pdfUrl: string) {
  console.log(`Fetching PDF from ${pdfUrl}...`);
  const res = await fetch(pdfUrl);
  if (!res.ok) throw new Error(`Failed to fetch PDF: ${res.status} ${res.statusText}`);
  const arrayBuffer = await res.arrayBuffer();
  const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
  const pdfDoc: any = await loadingTask.promise;
  const numPages = pdfDoc.numPages;
  console.log(`PDF loaded. ${numPages} pages found.`);

  const allRules: Array<{ code: string; title: string; text: string }> = [];

  for (let i = 0; i < numPages; i++) {
    console.log(`\n=== Processing page ${i + 1}/${numPages} ===`);
    const page = await pdfDoc.getPage(i + 1);
    const content = await page.getTextContent();
    const pageText = content.items.map((item: any) => item.str.trim()).join(' ');

    const prompt = `
You are an expert legal assistant. Extract all rules of civil procedure from the following page of the Florida Rules PDF. For each rule, return a JSON array of objects with keys:
- "code": the rule number (e.g., "1.010")
- "title": the rule title
- "text": the full text of that rule (as present on this page)

If no rule bodies are found on this page, return an empty array.

Page text:
${pageText}
`;

    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY not set in environment');
    }
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta2/models/gemini-2.5-pro-exp-03-25:generateMessage?key=${apiKey}`;

    const apiRes = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: { text: prompt },
        temperature: 0.0,
        maxOutputTokens: 1024
      })
    });

    if (!apiRes.ok) {
      console.error(`Error from Gemini API on page ${i + 1}:`, await apiRes.text());
      continue;
    }
    const json: any = await apiRes.json();
    const resultText = json.candidates?.[0]?.content;
    if (!resultText) {
      console.warn(`No content returned for page ${i + 1}`);
      continue;
    }

    let rulesOnPage: any[] = [];
    try {
      rulesOnPage = JSON.parse(resultText);
      if (!Array.isArray(rulesOnPage)) {
        console.warn(`Unexpected API response format on page ${i + 1}`, resultText);
        continue;
      }
    } catch (e) {
      console.error(`Failed to parse JSON from API on page ${i + 1}:`, resultText);
      continue;
    }

    for (const rule of rulesOnPage) {
      if (rule.code && rule.title && rule.text) {
        allRules.push({ code: rule.code, title: rule.title, text: rule.text });
      }
    }
  }

  // Write output
  const outDir = path.resolve(__dirname, '../rules');
  if (!fs.existsSync(outDir)) fs.mkdirSync(outDir, { recursive: true });
  const outputPath = path.resolve(outDir, 'fl-gemini-output.json');
  fs.writeFileSync(outputPath, JSON.stringify(allRules, null, 2));
  console.log(`\nExtraction complete. Output written to ${outputPath}`);
}

const [,, pdfUrl] = process.argv;
if (!pdfUrl) {
  console.error('Usage: ts-node scripts/geminiParser.ts <PDF_URL>');
  process.exit(1);
}
parseFloridaRulesWithGemini(pdfUrl).catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
});
