/// <reference types="node" />

import 'dotenv/config';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import fetch from 'node-fetch'; 
import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface TocEntry { code: string; title: string; startPage: number; endPage: number }

async function extractRules(pdfUrl: string) {
  const tocPath = path.resolve(__dirname, '../rules/fl-toc.json');
  if (!fs.existsSync(tocPath)) throw new Error(`TOC file not found at ${tocPath}. Run geminiDiscover first.`);
  const toc: TocEntry[] = JSON.parse(fs.readFileSync(tocPath, 'utf8'));

  console.log(`Fetching PDF from ${pdfUrl}...`);
  const res = await fetch(pdfUrl);
  if (!res.ok) throw new Error(`Failed to fetch PDF: ${res.status}`);
  const data = await res.arrayBuffer();
  pdfjsLib.GlobalWorkerOptions.workerSrc = `pdfjs-dist/legacy/build/pdf.worker.mjs`;
  const loadingTask = pdfjsLib.getDocument({ data });
  const pdf = await loadingTask.promise;
  console.log(`PDF loaded. Proceeding to extract ${toc.length} rules.`);

  const model = 'gemini-2.5-pro-exp-03-25';
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) throw new Error('GEMINI_API_KEY missing');
  const apiUrl = `https://generativelanguage.googleapis.com/v1beta2/models/${model}:generateMessage?key=${apiKey}`;

  const results: Array<{ code: string; title: string; text: string }> = [];

  for (const entry of toc) {
    console.log(`\n--- Extracting ${entry.code}: ${entry.title} (pages ${entry.startPage}-${entry.endPage}) ---`);
    let combined = '';
    for (let p = entry.startPage; p <= entry.endPage; p++) {
      const page = await pdf.getPage(p);
      const content = await page.getTextContent();
      const txt = content.items.map((i: any) => i.str.trim()).join(' ');
      combined += txt + '\n';
    }

    const prompt = `Extract the full text of RULE ${entry.code} titled "${entry.title}" from the following text:\n${combined}`;
    const body = { prompt: { text: prompt }, temperature: 0.0, maxOutputTokens: 2048 };

    const apiRes = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body)
    });
    if (!apiRes.ok) {
      console.error(`Gemini API error for rule ${entry.code}:`, await apiRes.text());
      continue;
    }
    const json: any = await apiRes.json();
    const contentStr = json.candidates?.[0]?.content;
    if (!contentStr) {
      console.warn(`No content from Gemini for rule ${entry.code}`);
      continue;
    }

    let parsed: { code?: string; title?: string; text?: string };
    try {
      parsed = JSON.parse(contentStr);
      if (typeof parsed !== 'object' || parsed === null) {
        parsed = { text: contentStr };
      } else if (!parsed.text) {
        parsed = { text: contentStr };
      }
    } catch (e) {
      console.warn(`Failed to parse JSON for rule ${entry.code}, using raw response.`);
      parsed = { text: contentStr };
    }

    if (parsed.text) {
      results.push({ code: entry.code, title: entry.title, text: parsed.text });
    } else {
      console.warn(`Gemini did not return text for ${entry.code}`);
    }
  }

  const outDir = path.resolve(__dirname, '../rules');
  const outPath = path.resolve(outDir, 'fl-rules-gemini.json');
  fs.writeFileSync(outPath, JSON.stringify(results, null, 2));
  console.log(`\nExtraction complete. Results written to ${outPath}`);
}

const [,, pdfUrl] = process.argv;
if (!pdfUrl) {
  console.error('Usage: ts-node scripts/geminiExtract.ts <PDF_URL>');
  process.exit(1);
}

extractRules(pdfUrl).catch(err => { console.error(err); process.exit(1); });
