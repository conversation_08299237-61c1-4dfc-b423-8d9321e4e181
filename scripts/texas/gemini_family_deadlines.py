#!/usr/bin/env python3
"""
Texas Family Code Deadline Extraction using Gemini 2.0 Flash
Uses Google's Gemini 2.0 Flash to intelligently extract deadline rules from Texas Family Code
"""

import os
import sys
import json
import requests
import time
import yaml
from datetime import datetime
from dotenv import load_dotenv
import logging
from PyPDF2 import PdfReader

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()

# Configuration
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    logging.error("Error: GEMINI_API_KEY not found in environment variables.")
    sys.exit(1)

GEMINI_MODEL = 'gemini-2.0-flash'
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/{GEMINI_MODEL}:generateContent?key={GEMINI_API_KEY}"
REQUEST_DELAY_SECONDS = 3
MAX_RETRIES = 3
RETRY_DELAY_SECONDS = 10

HEADERS = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
}

def extract_pdf_text(pdf_path, output_path):
    """Extract text from Texas Family Code PDF"""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PdfReader(file)
            text = ''
            for page_num, page in enumerate(reader.pages):
                text += f'\n--- PAGE {page_num + 1} ---\n'
                text += page.extract_text()
        
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(text)
        
        logging.info(f'Successfully extracted {len(reader.pages)} pages')
        logging.info(f'Text length: {len(text)} characters')
        return True
    except Exception as e:
        logging.error(f'Error extracting PDF: {e}')
        return False

def call_gemini_api(prompt, max_tokens=4096):
    """Call Gemini 2.0 Flash API with retry logic"""
    
    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "topK": 40,
            "topP": 0.95,
            "maxOutputTokens": max_tokens,
        }
    }
    
    for attempt in range(MAX_RETRIES):
        try:
            response = requests.post(GEMINI_API_URL, headers=HEADERS, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        content = candidate['content']['parts'][0]['text']
                        return content
                    else:
                        logging.error(f"Unexpected response structure: {candidate}")
                        return None
                else:
                    logging.warning(f"No candidates in response: {result}")
                    return None
            else:
                logging.error(f"API error {response.status_code}: {response.text}")
                
        except Exception as e:
            logging.error(f"Request failed (attempt {attempt + 1}): {e}")
            
        if attempt < MAX_RETRIES - 1:
            time.sleep(RETRY_DELAY_SECONDS)
    
    return None

def extract_texas_family_deadlines_with_gemini(text_file_path):
    """Extract Texas family law deadline rules using Gemini 2.0 Flash"""
    
    with open(text_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content into manageable chunks
    chunk_size = 25000  # Approximately 25k characters per chunk for Gemini 2.0 Flash
    chunks = [content[i:i+chunk_size] for i in range(0, len(content), chunk_size)]
    
    logging.info(f"Processing {len(chunks)} chunks of Texas Family Code text")
    
    all_deadlines = []
    
    for i, chunk in enumerate(chunks):
        logging.info(f"Processing chunk {i+1}/{len(chunks)}")
        
        prompt = f"""
You are a family law attorney expert analyzing Texas Family Code. 

Extract ALL deadline-related rules from this text chunk. Focus on:

1. **Divorce/Dissolution Deadlines** (Titles 1-2)
2. **Child Support Deadlines** (Title 5)
3. **Custody/Conservatorship Deadlines** (Title 5)
4. **Domestic Violence Deadlines** (Title 4)
5. **Adoption Deadlines** (Title 2)
6. **Paternity Deadlines** (Title 5)
7. **Property Division Deadlines** (Title 1)
8. **Court Procedure Deadlines** (Various titles)
9. **Service Deadlines** (Various titles)
10. **Appeal Deadlines** (Various titles)
11. **Child Protection Deadlines** (Title 5)
12. **Grandparent Rights Deadlines** (Title 5)

For each deadline rule found, extract:
- Section number (e.g., "Section 6.708", "Section 153.015")
- Specific deadline (e.g., "60 days", "30 days", "within 20 days")
- Description of what must be done
- Trigger event (what starts the clock)
- Consequences of missing deadline
- Any extension provisions

Return your findings as a JSON array with this structure:
```json
[
  {{
    "rule_number": "Section 6.708",
    "deadline": "60 days",
    "description": "Petition for divorce must be filed",
    "trigger": "Residency requirement met",
    "consequence": "Court lacks jurisdiction",
    "extensions": "None specified",
    "category": "divorce_deadlines",
    "practice_area": "family_law"
  }}
]
```

Text to analyze:
{chunk}
"""
        
        result = call_gemini_api(prompt, max_tokens=4096)
        
        if result:
            try:
                # Extract JSON from the response (handle markdown code blocks)
                if '```json' in result:
                    json_start = result.find('```json') + 7
                    json_end = result.find('```', json_start)
                    json_str = result[json_start:json_end].strip()
                else:
                    json_start = result.find('[')
                    json_end = result.rfind(']') + 1
                    json_str = result[json_start:json_end] if json_start != -1 and json_end != -1 else result
                
                if json_str:
                    chunk_deadlines = json.loads(json_str)
                    if isinstance(chunk_deadlines, list):
                        all_deadlines.extend(chunk_deadlines)
                        logging.info(f"Extracted {len(chunk_deadlines)} deadlines from chunk {i+1}")
                    else:
                        # Single object, convert to list
                        all_deadlines.append(chunk_deadlines)
                        logging.info(f"Extracted 1 deadline from chunk {i+1}")
                else:
                    logging.warning(f"No JSON found in chunk {i+1} response")
                    
            except json.JSONDecodeError as e:
                logging.error(f"JSON decode error in chunk {i+1}: {e}")
                logging.debug(f"Raw response: {result[:500]}...")
        
        # Rate limiting
        time.sleep(REQUEST_DELAY_SECONDS)
    
    return all_deadlines

def create_structured_yaml(deadlines, output_path):
    """Convert extracted deadlines to structured YAML format"""
    
    # Group deadlines by category
    categories = {}
    for deadline in deadlines:
        # Skip invalid deadlines
        if not deadline.get('rule_number') or not deadline.get('deadline'):
            logging.warning(f"Skipping invalid deadline: {deadline}")
            continue
            
        category = deadline.get('category', 'other')
        if category not in categories:
            categories[category] = {
                'description': f'{category.replace("_", " ").title()} deadlines',
                'rules': {}
            }
        
        rule_key = deadline['rule_number'].replace('.', '_').replace(' ', '_').lower()
        categories[category]['rules'][rule_key] = {
            'rule': deadline['rule_number'],
            'deadline': deadline['deadline'],
            'description': deadline.get('description', 'No description'),
            'trigger': deadline.get('trigger', 'Not specified'),
            'consequence': deadline.get('consequence', 'Not specified'),
            'extensions': deadline.get('extensions', 'Not specified')
        }
    
    # Create final structure
    texas_deadlines = {
        'jurisdiction': 'Texas',
        'court_type': 'Family',
        'practice_area': 'Family Law',
        'last_updated': '2024-12-31',
        'source': 'Texas Family Code',
        'extraction_method': 'Gemini 2.0 Flash AI Analysis',
        'extraction_date': datetime.now().isoformat(),
        'total_deadlines_extracted': len(deadlines),
        'deadline_categories': categories
    }
    
    # Save to YAML
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(texas_deadlines, f, default_flow_style=False, sort_keys=False, width=120)
    
    return texas_deadlines

if __name__ == "__main__":
    print("🤖 Using Gemini 2.0 Flash to extract Texas Family Code deadlines...")
    
    # First extract text from PDF
    pdf_file = "rules/Texas/sources/FAMILYCODE.pdf"
    text_file = "rules/Texas/extracted_text/family/family_code_raw.txt"
    output_file = "rules/Texas/processed/family_deadlines_gemini.yaml"
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(text_file), exist_ok=True)
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    if not os.path.exists(text_file):
        print("📄 Extracting text from Texas Family Code PDF...")
        if not extract_pdf_text(pdf_file, text_file):
            print("❌ Failed to extract PDF text")
            sys.exit(1)
    
    # Extract deadlines using Gemini
    deadlines = extract_texas_family_deadlines_with_gemini(text_file)
    
    if deadlines:
        # Create structured YAML
        result = create_structured_yaml(deadlines, output_file)
        
        print(f"\n✅ Successfully extracted {len(deadlines)} Texas family law deadline rules")
        print(f"📁 Saved to: {output_file}")
        
        # Print summary by category
        print(f"\n📊 Summary by category:")
        for category, data in result['deadline_categories'].items():
            print(f"   • {category}: {len(data['rules'])} rules")
    else:
        print("❌ No deadlines extracted. Check the logs for errors.")
