#!/usr/bin/env node
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

(async () => {
  const pdfjsLib = await import('pdfjs-dist/legacy/build/pdf.js');
  const pdfUrl = process.argv[2];
  if (!pdfUrl) {
    console.error('Usage: node scripts/geminiExtract.cjs <PDF_URL>');
    process.exit(1);
  }
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    console.error('GEMINI_API_KEY missing in .env');
    process.exit(1);
  }
  const model = 'gemini-2.5-pro-exp-03-25';
  const apiUrl = `https://generativelanguage.googleapis.com/v1beta2/models/${model}:generateMessage?key=${apiKey}`;

  // Load TOC
  const tocPath = path.resolve(__dirname, '../rules/fl-toc.json');
  if (!fs.existsSync(tocPath)) {
    console.error(`TOC file not found at ${tocPath}. Run geminiDiscover.cjs first.`);
    process.exit(1);
  }
  const toc = JSON.parse(fs.readFileSync(tocPath, 'utf8'));

  // Fetch PDF
  console.log(`Fetching PDF from ${pdfUrl}...`);
  const res = await fetch(pdfUrl);
  if (!res.ok) {
    console.error('Failed to fetch PDF:', res.status);
    process.exit(1);
  }
  const arrayBuffer = await res.arrayBuffer();
  const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
  const pdf = await loadingTask.promise;
  console.log(`PDF loaded. Proceeding to extract ${toc.length} rules.`);

  const results = [];
  for (const entry of toc) {
    console.log(`\n--- Extracting ${entry.code}: ${entry.title} (pages ${entry.startPage}-${entry.endPage}) ---`);
    let combined = '';
    for (let p = entry.startPage; p <= entry.endPage; p++) {
      const page = await pdf.getPage(p);
      const content = await page.getTextContent();
      const txt = content.items.map(i => i.str.trim()).join(' ');
      combined += txt + '\n';
    }
    const prompt = `Extract the full text of RULE ${entry.code} titled "${entry.title}" from the following text:\n${combined}`;
    const body = { prompt: { text: prompt }, temperature: 0.0, maxOutputTokens: 2048 };

    const apiRes = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body)
    });
    if (!apiRes.ok) {
      console.error(`API error for rule ${entry.code}:`, await apiRes.text());
      continue;
    }
    const json = await apiRes.json();
    const contentStr = json.candidates?.[0]?.content;
    if (!contentStr) {
      console.warn(`No response for ${entry.code}`);
      continue;
    }

    try {
      const parsed = JSON.parse(contentStr);
      if (parsed.text) {
        results.push({ code: entry.code, title: entry.title, text: parsed.text });
      } else {
        console.warn(`No text field for ${entry.code}`);
      }
    } catch (e) {
      console.warn(`Failed to parse JSON for ${entry.code}`, contentStr);
    }
  }

  const outDir = path.resolve(__dirname, '../rules');
  const outPath = path.resolve(outDir, 'fl-rules-gemini.json');
  fs.writeFileSync(outPath, JSON.stringify(results, null, 2));
  console.log(`Extraction complete: ${outPath}`);
})();
