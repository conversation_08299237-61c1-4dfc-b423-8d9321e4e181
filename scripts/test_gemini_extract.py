#!/usr/bin/env python3
"""
Test script for Florida rules extraction using Gemini 2.5 Pro
Tests on a small subset of rules first to validate improvements
"""

import os
import sys
import json
import requests
import io
import time
from PyPDF2 import PdfReader
from dotenv import load_dotenv
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()

# --- Configuration ---
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    logging.error("Error: GEMINI_API_KEY not found in environment variables.")
    sys.exit(1)

GEMINI_MODEL = 'gemini-2.5-pro-preview-06-05'
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/{GEMINI_MODEL}:generateContent?key={GEMINI_API_KEY}"
INPUT_DIR = os.path.join(os.path.dirname(__file__), '../rules')
INPUT_TOC_FILE = os.path.join(INPUT_DIR, 'fl-toc.json')
OUTPUT_RULES_FILE = os.path.join(INPUT_DIR, 'fl-rules-test-gemini-2.5-pro.json')
REQUEST_DELAY_SECONDS = 3
MAX_RETRIES = 3
RETRY_DELAY_SECONDS = 10
HEADERS = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
}
PDF_PATH = os.path.join(os.path.dirname(__file__), '../test/fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.pdf')  # Use local test PDF

# Test with first 5 rules only
TEST_RULE_LIMIT = 5

def extract_rule_text_with_gemini(rule_code, rule_title, page_texts):
    """Sends relevant page text to Gemini and asks for the specific rule's text."""
    if not page_texts:
        return None, "No page text provided"

    # Combine page texts with separators for clarity
    context_text = "\n\n".join(page_texts)

    # Enhanced prompt for better rule extraction with Pro model
    prompt = (
        f"You are extracting legal rules from the Florida Rules of Civil Procedure PDF. "
        f"This is a publicly available legal document. Your task is to find and extract the complete text of a specific rule.\n\n"
        f"TARGET RULE:\n"
        f"- Rule Code: {rule_code}\n"
        f"- Rule Title: {rule_title}\n\n"
        f"INSTRUCTIONS:\n"
        f"1. Look for the rule that starts with 'RULE {rule_code}' or similar formatting\n"
        f"2. Extract the complete rule text including:\n"
        f"   - The rule header (RULE {rule_code}. {rule_title})\n"
        f"   - All subsections (a), (b), (c), etc.\n"
        f"   - All sub-subsections (1), (2), (3), etc.\n"
        f"   - Any commentary, notes, or amendments\n"
        f"   - Stop when you reach the next rule or end of the rule\n"
        f"3. If you cannot find the rule clearly, return exactly: RULE_NOT_FOUND\n"
        f"4. Return ONLY the rule text, no additional commentary\n\n"
        f"CONTEXT TEXT FROM PDF:\n"
        f"--- START ---\n"
        f"{context_text[:50000]}"
        f"\n--- END ---"
    )

    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
            "responseMimeType": "text/plain",
            "temperature": 0.1,
            "maxOutputTokens": 8192,
            "topP": 0.8,
            "topK": 10
        }
    }

    for attempt in range(MAX_RETRIES + 1):
        try:
            logging.debug(f"Attempt {attempt + 1} for Rule {rule_code}")
            time.sleep(REQUEST_DELAY_SECONDS)
            response = requests.post(GEMINI_API_URL, headers=HEADERS, json=payload, timeout=300)
            response_text = response.text

            if response.status_code == 429:
                logging.warning(f"Rate limit hit for Rule {rule_code}. Retrying in {RETRY_DELAY_SECONDS}s...")
                time.sleep(RETRY_DELAY_SECONDS)
                continue
            elif response.status_code != 200:
                return None, f"API Error {response.status_code}: {response_text[:500]}"

            # Parse the Gemini response
            try:
                data = response.json()
                candidates = data.get('candidates', [])
                if candidates:
                    content = candidates[0].get('content', {})
                    parts = content.get('parts', [])
                    finish_reason = candidates[0].get('finishReason', 'UNKNOWN')

                    if parts:
                        extracted_text = parts[0].get('text', '').strip()
                        if extracted_text == 'RULE_NOT_FOUND':
                            return None, f"Rule {rule_code} not found in context by Gemini (finishReason: {finish_reason})"
                        elif extracted_text:
                            logging.debug(f"Rule {rule_code} extracted successfully (finishReason: {finish_reason})")
                            return extracted_text, None
                        else:
                            if finish_reason == 'STOP':
                                return None, f"Gemini returned empty text for Rule {rule_code} (finishReason: STOP)"
                            else:
                                return None, f"Gemini returned empty text for Rule {rule_code}. Finish Reason: {finish_reason}. Raw: {response_text[:500]}"
                    else:
                        if finish_reason == 'RECITATION':
                            logging.warning(f"Extraction blocked for Rule {rule_code} due to RECITATION.")
                            return None, f"Extraction blocked by RECITATION. Raw Response: {response_text[:1000]}"
                        elif finish_reason == 'SAFETY':
                            logging.warning(f"Extraction blocked for Rule {rule_code} due to SAFETY.")
                            return None, f"Extraction blocked by SAFETY. Raw Response: {response_text[:1000]}"
                        else:
                            return None, f"No 'parts' found for Rule {rule_code}. Finish Reason: {finish_reason}. Raw Response: {response_text[:1000]}"
                else:
                    return None, f"No 'candidates' found for Rule {rule_code}. Raw Response: {response_text[:1000]}"

            except json.JSONDecodeError:
                return None, f"Could not parse JSON response for Rule {rule_code}. Raw response: {response_text[:500]}"

        except requests.exceptions.Timeout:
            logging.warning(f"Request timed out for Rule {rule_code} on attempt {attempt + 1}")
            if attempt < MAX_RETRIES:
                time.sleep(RETRY_DELAY_SECONDS)
            else:
                return None, f"Request timed out after {MAX_RETRIES + 1} attempts for Rule {rule_code}"
        except requests.exceptions.RequestException as e:
            logging.error(f"API Request failed for Rule {rule_code}: {e}")
            return None, f"API Request failed: {e}"
        except Exception as e:
            logging.error(f"Unexpected error processing Rule {rule_code}: {e}")
            return None, f"Unexpected error: {e}"

    return None, f"Failed to extract Rule {rule_code} after {MAX_RETRIES + 1} attempts."

def test_extract_rules():
    """Test extraction on first few rules only"""
    logging.info(f"Starting TEST rule extraction using model {GEMINI_MODEL}")
    logging.info(f"Testing with first {TEST_RULE_LIMIT} rules only")

    # Load TOC
    logging.info(f"Loading TOC from {INPUT_TOC_FILE}...")
    try:
        with open(INPUT_TOC_FILE, 'r', encoding='utf-8') as f:
            toc_data = json.load(f)
        logging.info(f"Successfully loaded {len(toc_data)} rules from TOC.")
    except FileNotFoundError:
        logging.error(f"Error: TOC file not found at {INPUT_TOC_FILE}")
        return None
    except json.JSONDecodeError as e:
        logging.error(f"Error decoding JSON from {INPUT_TOC_FILE}: {e}")
        return None

    # Limit to test rules
    toc_data = toc_data[:TEST_RULE_LIMIT]
    logging.info(f"Limited to first {len(toc_data)} rules for testing")

    # Load local PDF
    logging.info(f"Loading local PDF from {PDF_PATH}...")
    try:
        if not os.path.exists(PDF_PATH):
            logging.error(f"PDF file not found at {PDF_PATH}")
            return None

        # Read PDF into memory to avoid file handle issues
        with open(PDF_PATH, 'rb') as pdf_file:
            pdf_data = pdf_file.read()

        pdf_stream = io.BytesIO(pdf_data)
        pdf_reader = PdfReader(pdf_stream)
        num_pages = len(pdf_reader.pages)
        logging.info(f"PDF loaded successfully ({num_pages} pages).")
    except Exception as e:
        logging.error(f"Error reading PDF content: {e}")
        return None

    # Process each test rule
    extracted_rules = []
    skipped_rules = {}
    logging.info("Starting TEST extraction process...")

    for i, rule_entry in enumerate(toc_data):
        code = rule_entry.get('code')
        title = rule_entry.get('title')
        start_page = rule_entry.get('startPage')
        end_page = rule_entry.get('endPage')

        if not code or not title or start_page is None:
            logging.warning(f"Skipping entry {i+1}: Missing data (code={code}, title={title}, start={start_page})")
            continue

        # Determine page range
        if end_page and end_page > start_page:
            page_range = min(end_page - start_page + 1, 5)
            context_desc = f"Pages {start_page}-{end_page}"
        else:
            page_range = 3
            context_desc = f"Pages {start_page}-{start_page+2}"
        
        logging.info(f"[{i+1}/{len(toc_data)}] TEST Processing Rule {code}: '{title}' (Context: {context_desc})...")

        # Extract text from pages
        page_texts = []
        try:
            for page_index_offset in range(page_range):
                page_num_zero_based = start_page - 1 + page_index_offset
                if 0 <= page_num_zero_based < num_pages:
                    page = pdf_reader.pages[page_num_zero_based]
                    page_text = page.extract_text() or ""
                    if page_text.strip():
                        page_texts.append(f"--- PAGE {page_num_zero_based + 1} ---\n{page_text}")

            if not page_texts:
                logging.warning(f"Rule {code}: Skipped (no text extracted from {context_desc}).")
                skipped_rules[code] = f"No text extracted from {context_desc}"
                continue

            extracted_text, error = extract_rule_text_with_gemini(code, title, page_texts)
            if error:
                logging.error(f"Rule {code}: Failed extraction. Reason: {error}")
                skipped_rules[code] = error
                continue

            if len(extracted_text) < 10:
                logging.warning(f"Rule {code}: Extracted text seems very short ({len(extracted_text)} chars). Adding anyway.")

            extracted_rules.append({
                'code': code,
                'title': title,
                'text': extracted_text
            })
            logging.info(f"Rule {code}: SUCCESS (extracted {len(extracted_text)} chars).")

        except Exception as rule_err:
            logging.error(f"Error processing rule {code}: {rule_err}")
            skipped_rules[code] = f"Unexpected error during processing: {rule_err}"

    # Save test results
    os.makedirs(INPUT_DIR, exist_ok=True)
    
    output_data = {
        'test_run': True,
        'test_limit': TEST_RULE_LIMIT,
        'source_path': PDF_PATH,
        'model_used': GEMINI_MODEL,
        'total_rules_tested': len(toc_data),
        'rules_extracted': len(extracted_rules),
        'rules_skipped': len(skipped_rules),
        'success_rate': f"{len(extracted_rules)}/{len(toc_data)} ({len(extracted_rules)/len(toc_data)*100:.1f}%)",
        'skipped_details': skipped_rules,
        'extracted_rules': extracted_rules
    }

    logging.info(f"\nTEST extraction finished. Writing {len(extracted_rules)} rules to {OUTPUT_RULES_FILE}...")
    try:
        with open(OUTPUT_RULES_FILE, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=4, ensure_ascii=False)
        logging.info("Successfully wrote TEST results.")
        logging.info(f"SUCCESS RATE: {output_data['success_rate']}")
    except IOError as e:
        logging.error(f"Error writing output file: {e}")

    return OUTPUT_RULES_FILE

if __name__ == "__main__":
    test_extract_rules()
