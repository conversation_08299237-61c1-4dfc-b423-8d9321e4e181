#!/usr/bin/env python3
"""
Search specifically for RULE 1 content in the PDF
"""

import os
import io
from PyPDF2 import PdfReader

PDF_PATH = os.path.join(os.path.dirname(__file__), '../test/fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.pdf')

def search_rule_1():
    """Search for RULE 1 actual content"""
    print(f"Loading PDF from {PDF_PATH}...")
    
    try:
        with open(PDF_PATH, 'rb') as pdf_file:
            pdf_data = pdf_file.read()
        
        pdf_stream = io.BytesIO(pdf_data)
        pdf_reader = PdfReader(pdf_stream)
        num_pages = len(pdf_reader.pages)
        print(f"PDF loaded successfully ({num_pages} pages).")
        
        # Search for RULE 1 content specifically
        for page_num in range(num_pages):
            page = pdf_reader.pages[page_num]
            page_text = page.extract_text() or ""
            
            # Look for RULE 1 with actual content (not table of contents)
            if "RULE 1." in page_text:
                # Check if this looks like actual rule content
                lines = page_text.split('\n')
                rule_1_found = False
                
                for i, line in enumerate(lines):
                    if "RULE 1." in line and "OBJECTIVE" in line:
                        # Check if there's content after the rule title
                        remaining_lines = lines[i+1:i+10]  # Check next 10 lines
                        content_after = '\n'.join(remaining_lines).strip()
                        
                        if len(content_after) > 50 and not content_after.startswith("RULE"):
                            print(f"\n{'='*60}")
                            print(f"FOUND RULE 1 CONTENT ON PAGE {page_num + 1}")
                            print(f"{'='*60}")
                            
                            # Show the rule and surrounding context
                            start_idx = max(0, i-2)
                            end_idx = min(len(lines), i+15)
                            context = '\n'.join(lines[start_idx:end_idx])
                            print(context)
                            rule_1_found = True
                            break
                
                if rule_1_found:
                    break
        else:
            print("Could not find RULE 1 content")
            
            # Let's search for any page that has "OBJECTIVE OF RULES" with content
            print("\nSearching for 'OBJECTIVE OF RULES' with content...")
            for page_num in range(num_pages):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text() or ""
                
                if "OBJECTIVE OF RULES" in page_text and len(page_text) > 200:
                    print(f"\n--- PAGE {page_num + 1} with OBJECTIVE OF RULES ---")
                    print(page_text[:1500])
                    break
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    search_rule_1()
