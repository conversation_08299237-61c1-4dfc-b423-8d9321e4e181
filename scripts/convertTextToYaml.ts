import yaml from 'js-yaml';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

interface Rule {
  number: string;
  title: string;
  body: string;
}

export function convertTextToYaml(text: string, jurisdiction: string): string {
  const lines = text.split('\n').filter(line => !line.includes('--- Page Break ---')); // Filter out page breaks
  const rules: Rule[] = [];
  let currentRule: Rule | null = null;

  // Regex for multi-line start (Rule Number only)
  const ruleStartMultiLineRegex = /^RULE\s+(\d+[a-z]?)\.?\s*$/i;
  // Regex for single-line start (Rule Number and Title)
  const ruleStartSingleLineRegex = /^RULE\s+(\d+[a-z]?)\.?\s+(.*)$/i;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    if (!trimmedLine) continue; // Skip empty lines

    let ruleNumber: string | null = null;
    let ruleTitle: string | null = null;
    let matched = false;

    // --- Attempt 1: Match multi-line format (RULE number only) ---
    const multiLineMatch = ruleStartMultiLineRegex.exec(trimmedLine);
    if (multiLineMatch) {
      const potentialRuleNumber = multiLineMatch[1];
      // Look ahead for the title on the next non-empty line
      let titleLineIndex = i + 1;
      while (titleLineIndex < lines.length && !lines[titleLineIndex].trim()) {
        titleLineIndex++; // Skip blank lines
      }
      if (titleLineIndex < lines.length) {
        // Found a potential title on a subsequent line
        ruleNumber = potentialRuleNumber;
        ruleTitle = lines[titleLineIndex].trim();
        i = titleLineIndex; // Advance loop index past the title line we consumed
        matched = true;
      } else {
        // Found rule number line, but no subsequent title line before EOF
        // Treat it as a rule with no title for now, or potentially log a warning
        console.warn(`Warning: Found multi-line rule start '${trimmedLine}' but no subsequent title line.`);
        ruleNumber = potentialRuleNumber;
        ruleTitle = ''; // Assign empty title
        matched = true;
      }
    }

    // --- Attempt 2: If multi-line didn't match, try single-line format --- 
    if (!matched) {
      const singleLineMatch = ruleStartSingleLineRegex.exec(trimmedLine);
      if (singleLineMatch) {
        ruleNumber = singleLineMatch[1];
        ruleTitle = singleLineMatch[2].trim();
        matched = true;
      }
    }

    // --- Process the match (if any) ---
    if (matched && ruleNumber !== null && ruleTitle !== null) {
      // Finalize the previous rule (if exists)
      if (currentRule) {
        currentRule.body = currentRule.body.trim();
        rules.push(currentRule);
      }
      // Start the new rule object
      currentRule = {
        number: ruleNumber,
        title: ruleTitle,
        body: '' // Initialize body
      };
    } else if (currentRule) {
      // --- Part of the current rule's body --- 
      // Append non-matching line to the current rule's body
      currentRule.body += (currentRule.body ? '\n' : '') + trimmedLine;
    }
    // Ignore lines before the first rule is found or lines that didn't match anything
  }

  // Add the last processed rule
  if (currentRule) {
    currentRule.body = currentRule.body.trim();
    rules.push(currentRule);
  }

  // Use expected metadata for this fixture
  const data = {
    meta: {
        jurisdiction: jurisdiction,
        version: "2020-05-01",
        source: "Texas Rules of Civil Procedure"
    },
    rules: rules.map(r => ({
        code: `RULE_${r.number}`.toUpperCase().replace(/\.$/, ''), // Ensure code is like RULE_1, RULE_21A
        title: r.title,
        description: r.body
    }))
  };

  return yaml.dump(data);
}

// Optional: Example usage if run directly
/*
async function main() {
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  const fixtureDir = path.resolve(__dirname, '../test/fixtures');
  const textFilePath = path.join(fixtureDir, 'trcp-all-updated-with-amendments-effective-may-1-2020.raw_text.txt');
  const outputYamlPath = path.join(fixtureDir, 'trcp-all-updated-with-amendments-effective-may-1-2020.generated_from_text_v3.yaml');

  try {
    console.log(`Reading text from: ${textFilePath}`);
    const rawText = await fs.readFile(textFilePath, 'utf8');
    console.log('Converting text to YAML...');
    const yamlContent = convertTextToYaml(rawText, 'tx-civil');
    console.log(`Writing YAML to: ${outputYamlPath}`);
    await fs.writeFile(outputYamlPath, yamlContent, 'utf8');
    console.log('YAML generation complete.');
  } catch (error) {
    console.error('Error during conversion:', error);
  }
}

// main();
*/
