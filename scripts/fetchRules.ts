#!/usr/bin/env ts-node

import fetch from 'node-fetch';
// Import the legacy build for Node.js
// @ts-ignore - Suppress TS2307 error for missing legacy types
import * as pdfjs from 'pdfjs-dist/legacy/build/pdf.mjs';
import type { TextItem, TextMarkedContent } from 'pdfjs-dist/types/src/display/api.js';
import * as fs from 'node:fs/promises'; // Use node: prefix
import yaml from 'js-yaml';
import { diff } from 'deep-diff';
import * as path from 'node:path'; // Use node: prefix
import { fileURLToPath } from 'node:url'; // Use node: prefix
import { Buffer } from 'node:buffer'; // Import Buffer
import process from 'node:process'; // Import process

import sources from './sources.json' with { type: 'json' };

// Derive __dirname for ES Modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Remove worker setup - let pdfjs-dist handle it internally for legacy Node build

async function main() {
  const logDir = path.resolve(__dirname, '../logs');
  const logPath = path.join(logDir, 'fetchRules.log');
  await fs.mkdir(logDir, { recursive: true });

  for (const [jurisdiction, url] of Object.entries(sources) as [string, string][]) {
    try {
      console.log(`[${jurisdiction}] Fetching ${url}`);
      const res = await fetch(url);
      if (!res.ok) throw new Error(`Fetch failed: ${res.status} ${res.statusText}`);
      const buffer = Buffer.from(await res.arrayBuffer());
      
      const loadingTask = pdfjs.getDocument({ data: new Uint8Array(buffer) });
      const doc = await loadingTask.promise;
      let fullText = '';
      for (let i = 1; i <= doc.numPages; i++) {
        const page = await doc.getPage(i);
        const textContent = await page.getTextContent();
        // Ensure items are TextItem before accessing str
        const pageText = textContent.items
          .map((item: TextItem | TextMarkedContent) => ('str' in item ? item.str : ''))
          .join(' '); // Join text items with space
        fullText += pageText + '\n'; // Add newline between pages
      }
      const text = fullText.trim();

      // Convert text to YAML
      const yamlContent = convertTextToYaml(text, jurisdiction);
      // Correct path: go up two levels from dist/scripts to project root, then into rules
      const rulesPath = path.resolve(__dirname, `../../rules/${jurisdiction}.yaml`);
      
      // Check if file exists before reading
      let existing = '';
      try {
        existing = await fs.readFile(rulesPath, 'utf8');
      } catch (readError: any) {
        if (readError.code !== 'ENOENT') {
          // If error is something other than 'file not found', rethrow it
          throw readError;
        } 
        // If file doesn't exist, 'existing' remains empty, proceed to write
        console.log(`[${jurisdiction}] No existing file found at ${rulesPath}. Creating new file.`);
      }

      if (existing.trim() !== yamlContent.trim()) {
        console.log(`[${jurisdiction}] Changes detected or new file, writing to ${rulesPath}`);
        await fs.writeFile(rulesPath, yamlContent, 'utf8');
        // TODO: commit or open PR using GitHub CLI
      } else {
        console.log(`[${jurisdiction}] No changes detected`);
      }
    } catch (err) {
      console.error(`[${jurisdiction}] Error fetching/parsing:`, err);
    }
  }

  // Optionally write a summary to the log file
}

export function convertTextToYaml(text: string, jurisdiction: string): string {
  const sourceUrl = sources[jurisdiction as keyof typeof sources] || 'Unknown';
  let effectiveDate = 'YYYY-MM-DD'; // Default placeholder
  let description = `Placeholder Rules for ${jurisdiction}`;
  let rules: { code: string; title: string; text?: string }[] = []; // Add optional text field

  if (jurisdiction === 'FL_STATE') {
    description = 'Florida Rules of Civil Procedure';

    // --- Step 1: Extract rules from TOC & Estimate TOC End ---
    const tocRules: { code: string; title: string }[] = [];
    const tocRegex = /RULE\s+(\d+\.\d+)\.?\s+([^\.]+?)\s*\.{2,}/gm;
    let tocMatch;
    const tocText = text.substring(0, 20000); // Search a larger TOC area just in case
    let lastTocMatchIndex = 0;
    while ((tocMatch = tocRegex.exec(tocText)) !== null) {
        const title = tocMatch[2].trim();
        if (title && title !== '[REPEALED AUG') {
          tocRules.push({ code: tocMatch[1], title: title });
          lastTocMatchIndex = tocMatch.index + tocMatch[0].length; // Update last match end position
        }
    }
    if (tocRules.length === 0) {
        console.error(`[${jurisdiction}] No rules found in TOC! Aborting text extraction.`);
        // Handle error...
    }

    // --- Set Search Start Index based on Last TOC Match ---
    const searchStartIndex = lastTocMatchIndex + 10000; // Increase buffer significantly
    console.log(`[${jurisdiction}] Estimated TOC end index: ${lastTocMatchIndex}`);
    console.log(`[${jurisdiction}] Starting search for rule bodies at index: ${searchStartIndex}`);
    console.log(`[${jurisdiction}] Sample text slice around search start index ${searchStartIndex}:\n---\n${text.substring(searchStartIndex - 200, searchStartIndex + 1000)}\n---`); // Log area around new start


    // --- Step 2 & 3: Find rule text boundaries in main body ---
    let rules: { code: string; title: string; text?: string }[] = []; // Re-initialize rules array here
    for (let i = 0; i < tocRules.length; i++) {
      const currentRule = tocRules[i];
      // Regex: Start of line, optional whitespace, RULE, code, optional period, whitespace/end-of-line
      const startRegex = new RegExp(`^\\s*RULE\\s+${currentRule.code.replace('.', '\\.')}\\.?(?:\\s|$)`, 'im'); // Added ^\s*
      if (i === 0) {
          console.log(`[${jurisdiction}] Regex for first rule (${currentRule.code}): ${startRegex}`);
      }
      const ruleSearchArea = text.substring(searchStartIndex);
      const startMatch = startRegex.exec(ruleSearchArea);

      if (!startMatch) {
         // Keep simplified fallback for now
         console.warn(`[${jurisdiction}] Could not find start for rule ${currentRule.code} after index ${searchStartIndex}.`);
         rules.push({ code: currentRule.code, title: currentRule.title });
         continue;
      }

      // Adjust index back relative to the full text
      // Need to account for potential leading whitespace matched by \s*
      const startIndex = searchStartIndex + startMatch.index + startMatch[0].indexOf(`RULE ${currentRule.code}`); // Adjust start to the actual "RULE" text
      let endIndex = text.length;

      // Find the start of the *next* rule to determine the end boundary
      if (i + 1 < tocRules.length) {
        const nextRule = tocRules[i + 1];
        const nextStartRegex = new RegExp(`^\\s*RULE\\s+${nextRule.code.replace('.', '\\.')}\\.?(?:\\s|$)`, 'im'); // Use same flexible regex
        const nextStartMatch = nextStartRegex.exec(text.substring(startIndex + 10)); // Search after current rule's start

        if (nextStartMatch) {
          // Adjust index back relative to the full text, accounting for \s*
          endIndex = startIndex + 10 + nextStartMatch.index + nextStartMatch[0].indexOf(`RULE ${nextRule.code}`);
        } else {
             console.warn(`[${jurisdiction}] Could not find start for next rule ${nextRule.code} to delimit ${currentRule.code}`);
        }
      }

      // --- Step 4: Extract and clean rule text ---
      const ruleText = text.substring(startIndex, endIndex).trim();

       // --- Step 5: Add to final rules array ---
       rules.push({
         code: currentRule.code,
         title: currentRule.title,
         text: ruleText // Add the extracted text
       });
    }

    // Basic YAML structure for Florida
    const data = {
      meta: {
        jurisdiction: jurisdiction.replace('_', '-').toLowerCase(), // fl-state
        source_url: sourceUrl,
        effective_date: effectiveDate,
        description: description,
        version: '1.0.0' // Placeholder version
      },
      rules: rules, // Use the array with text
      triggers: [ { code: 'PLACEHOLDER_TRIGGER', description: 'Placeholder event description' } ],
      deadlines: [ { trigger_code: 'PLACEHOLDER_TRIGGER', condition: 'default', days: 0, direction: 'forward', unit: 'calendar_days', description: 'Placeholder deadline description' } ],
      holidays: { use_builtin: [], custom: [] }
    };
    return yaml.dump(data, { lineWidth: -1 }); // Prevent line wrapping for long titles

  } else {
    // Keep original placeholder logic for other states
    const data = {
      meta: {
        jurisdiction: jurisdiction.replace('_', '-').toLowerCase(),
        source_url: sourceUrl,
        effective_date: effectiveDate,
        description: description,
        version: '1.0.0'
      },
      triggers: [ { code: 'PLACEHOLDER_TRIGGER', description: 'Placeholder event description' } ],
      deadlines: [ { trigger_code: 'PLACEHOLDER_TRIGGER', condition: 'default', days: 0, direction: 'forward', unit: 'calendar_days', description: 'Placeholder deadline description' } ],
      holidays: { use_builtin: [], custom: [] },
      raw_text_snippet: text.substring(0, 500) + (text.length > 500 ? '...' : '')
    };
    return yaml.dump(data);
  }
}

main().catch(err => {
  console.error('Fatal error in fetchRules:', err);
  process.exit(1);
});
