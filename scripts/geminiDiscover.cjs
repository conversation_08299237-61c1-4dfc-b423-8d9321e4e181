#!/usr/bin/env node
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

(async () => {
  const pdfjsLib = await import('pdfjs-dist/legacy/build/pdf.js');
  const pdfUrl = process.argv[2];
  if (!pdfUrl) {
    console.error('Usage: node scripts/geminiDiscover.cjs <PDF_URL>');
    process.exit(1);
  }
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    console.error('GEMINI_API_KEY missing in .env');
    process.exit(1);
  }
  const model = 'gemini-2.5-pro-exp-03-25';
  const apiUrl = `https://generativelanguage.googleapis.com/v1beta2/models/${model}:generateMessage?key=${apiKey}`;

  const raw = [];
  const res = await fetch(pdfUrl);
  if (!res.ok) {
    console.error('Failed to fetch PDF:', res.status);
    process.exit(1);
  }
  const arrayBuffer = await res.arrayBuffer();
  const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
  const pdf = await loadingTask.promise;
  const numPages = pdf.numPages;
  console.log(`PDF loaded. Pages: ${numPages}`);

  for (let i = 1; i <= Math.min(20, numPages); i++) {
    console.log(`Scanning page ${i}`);
    const page = await pdf.getPage(i);
    const content = await page.getTextContent();
    const text = content.items.map(item => item.str.trim()).join(' ');
    const prompt = `Extract table of contents entries for Rules of Civil Procedure from this page. Return a JSON array of objects { code, title }. PageNo:${i}\n\n${text}`;
    const body = { prompt: { text: prompt }, temperature: 0.0, maxOutputTokens: 512 };
    const apiRes = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body) });
    if (!apiRes.ok) {
      console.error(`API error page ${i}:`, await apiRes.text());
      continue;
    }
    const json = await apiRes.json();
    const contentStr = json.candidates?.[0]?.content;
    if (!contentStr) continue;
    try {
      const arr = JSON.parse(contentStr);
      arr.forEach(e => raw.push({ code: e.code, title: e.title, page: i }));
    } catch (e) {
      console.warn(`Failed to parse JSON on page ${i}`);
    }
  }

  const map = new Map();
  raw.forEach(e => {
    if (!map.has(e.code)) map.set(e.code, { code: e.code, title: e.title, startPage: e.page, endPage: e.page });
    else map.get(e.code).endPage = e.page;
  });
  const toc = Array.from(map.values());

  const outDir = path.resolve(__dirname, '../rules');
  if (!fs.existsSync(outDir)) fs.mkdirSync(outDir, { recursive: true });
  const outPath = path.resolve(outDir, 'fl-toc.json');
  fs.writeFileSync(outPath, JSON.stringify(toc, null, 2));
  console.log(`TOC saved to ${outPath}`);
})();
