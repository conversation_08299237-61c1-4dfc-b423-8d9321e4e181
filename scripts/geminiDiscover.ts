/// <reference types="node" />

import 'dotenv/config';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import fetch from 'node-fetch'; 
import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf.mjs';

// ESM __dirname shim
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface TocEntryRaw { code: string; title: string; page: number }
interface TocEntry { code: string; title: string; startPage: number; endPage: number }

async function discoverTOC(pdfUrl: string) {
  console.log(`Fetching PDF from ${pdfUrl}...`);
  const res = await fetch(pdfUrl);
  if (!res.ok) throw new Error(`Failed to fetch PDF: ${res.status}`);
  const data = await res.arrayBuffer();
  // Set workerSrc to avoid issues in Node.js environment - use legacy worker too
  pdfjsLib.GlobalWorkerOptions.workerSrc = `pdfjs-dist/legacy/build/pdf.worker.mjs`;
  const loadingTask = pdfjsLib.getDocument({ data });
  const pdf = await loadingTask.promise;
  const numPages = pdf.numPages;
  console.log(`PDF loaded (${numPages} pages). Discovering TOC...`);

  const rawEntries: TocEntryRaw[] = [];
  const model = 'gemini-2.5-pro-exp-03-25';
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) throw new Error('GEMINI_API_KEY missing');
  const apiUrl = `https://generativelanguage.googleapis.com/v1beta2/models/${model}:generateMessage?key=${apiKey}`;

  // Only scan first 20 pages for TOC
  for (let i = 1; i <= Math.min(20, numPages); i++) {
    const page = await pdf.getPage(i);
    const content = await page.getTextContent();
    const text = content.items.map((x: any) => x.str.trim()).join(' ');
    console.log(`Page ${i}: scanning for TOC entries...`);
    const prompt = `Extract table of contents entries for Rules of Civil Procedure from this page. Return a JSON array of objects { code, title }, or [] if none. PageNo:${i}\n\n${text}`;
    const body = { prompt: { text: prompt }, temperature: 0.0, maxOutputTokens: 512 };
    const apiRes = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body) });
    if (!apiRes.ok) {
      console.error(`Gemini error page ${i}:`, await apiRes.text());
      continue;
    }
    const json: any = await apiRes.json();
    const contentStr = json.candidates?.[0]?.content;
    if (!contentStr) continue;
    try {
      const arr = JSON.parse(contentStr) as Array<{ code: string; title: string }>;
      arr.forEach(e => rawEntries.push({ code: e.code, title: e.title, page: i }));
    } catch {
      console.warn(`Could not parse TOC JSON on page ${i}`);
    }
  }

  // Group into start/end page
  const map = new Map<string, TocEntry>();
  rawEntries.forEach(e => {
    const key = e.code;
    if (!map.has(key)) {
      map.set(key, { code: e.code, title: e.title, startPage: e.page, endPage: e.page });
    } else {
      const v = map.get(key)!;
      v.endPage = e.page;
    }
  });
  const toc = Array.from(map.values());

  const outDir = path.resolve(__dirname, '../rules');
  if (!fs.existsSync(outDir)) fs.mkdirSync(outDir, { recursive: true });
  const outPath = path.resolve(outDir, 'fl-toc.json');
  fs.writeFileSync(outPath, JSON.stringify(toc, null, 2));
  console.log(`Discovered ${toc.length} TOC entries. Written to ${outPath}`);
}

const [,, pdfUrl] = process.argv;
if (!pdfUrl) {
  console.error('Usage: ts-node scripts/geminiDiscover.ts <PDF_URL>');
  process.exit(1);
}

discoverTOC(pdfUrl).catch(err => { console.error(err); process.exit(1); });
