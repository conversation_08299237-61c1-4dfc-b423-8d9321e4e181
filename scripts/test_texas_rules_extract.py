#!/usr/bin/env python3
"""
Test script for Texas rules extraction using Gemini 2.5 Pro
Tests on Texas Rules PDF to validate the Gemini 2.5 Pro setup
"""

import os
import sys
import json
import io
import time
import requests
from PyPDF2 import PdfReader
from dotenv import load_dotenv
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()

# --- Configuration ---
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    logging.error("Error: GEMINI_API_KEY not found in environment variables.")
    sys.exit(1)

GEMINI_MODEL = 'gemini-2.5-pro-preview-06-05'
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/{GEMINI_MODEL}:generateContent?key={GEMINI_API_KEY}"
PDF_PATH = os.path.join(os.path.dirname(__file__), '../test/fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.pdf')
OUTPUT_FILE = os.path.join(os.path.dirname(__file__), '../rules/texas-rules-test-gemini-2.5-pro.json')
REQUEST_DELAY_SECONDS = 3
MAX_RETRIES = 3
RETRY_DELAY_SECONDS = 10
HEADERS = {
    'Content-Type': 'application/json',
}

# Test with first few Texas rules (actual content starts around page 29)
TEST_RULES = [
    {"code": "1", "title": "OBJECTIVE OF RULES", "start_page": 29},
    {"code": "2", "title": "SCOPE OF RULES", "start_page": 29},
    {"code": "3", "title": "CONSTRUCTION OF RULES", "start_page": 29},
    {"code": "4", "title": "COMPUTATION OF TIME", "start_page": 30},
    {"code": "5", "title": "ENLARGEMENT OF TIME", "start_page": 30}
]

def extract_rule_text_with_gemini(rule_code, rule_title, page_texts):
    """Sends relevant page text to Gemini and asks for the specific rule's text."""
    if not page_texts:
        return None, "No page text provided"

    # Combine page texts
    context_text = "\n\n".join(page_texts)

    # Enhanced prompt for Texas rules
    prompt = (
        f"You are extracting legal rules from the Texas Rules of Civil Procedure PDF. "
        f"This is a publicly available legal document. Your task is to find and extract the complete text of a specific rule.\n\n"
        f"TARGET RULE:\n"
        f"- Rule Code: RULE {rule_code}\n"
        f"- Rule Title: {rule_title}\n\n"
        f"INSTRUCTIONS:\n"
        f"1. Look for the rule that starts with 'RULE {rule_code}.' or 'RULE {rule_code} ' (note the period or space)\n"
        f"2. Extract the complete rule text including:\n"
        f"   - The rule header (RULE {rule_code}. {rule_title})\n"
        f"   - All rule content until the next rule starts\n"
        f"   - Any subsections, commentary, or notes\n"
        f"3. If you cannot find the rule clearly, return exactly: RULE_NOT_FOUND\n"
        f"4. Return ONLY the rule text, no additional commentary\n\n"
        f"CONTEXT TEXT FROM PDF:\n"
        f"--- START ---\n"
        f"{context_text[:50000]}"
        f"\n--- END ---"
    )

    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
            "responseMimeType": "text/plain",
            "temperature": 0.1,
            "maxOutputTokens": 8192,
            "topP": 0.8,
            "topK": 10
        }
    }

    for attempt in range(MAX_RETRIES + 1):
        try:
            logging.debug(f"Attempt {attempt + 1} for Rule {rule_code}")
            time.sleep(REQUEST_DELAY_SECONDS)
            response = requests.post(GEMINI_API_URL, headers=HEADERS, json=payload, timeout=300)
            response_text = response.text

            if response.status_code == 429:
                logging.warning(f"Rate limit hit for Rule {rule_code}. Retrying in {RETRY_DELAY_SECONDS}s...")
                time.sleep(RETRY_DELAY_SECONDS)
                continue
            elif response.status_code != 200:
                return None, f"API Error {response.status_code}: {response_text[:500]}"

            # Parse the Gemini response
            try:
                data = response.json()
                candidates = data.get('candidates', [])
                if candidates:
                    content = candidates[0].get('content', {})
                    parts = content.get('parts', [])
                    finish_reason = candidates[0].get('finishReason', 'UNKNOWN')

                    if parts:
                        extracted_text = parts[0].get('text', '').strip()
                        if extracted_text == 'RULE_NOT_FOUND':
                            return None, f"Rule {rule_code} not found in context by Gemini (finishReason: {finish_reason})"
                        elif extracted_text:
                            logging.debug(f"Rule {rule_code} extracted successfully (finishReason: {finish_reason})")
                            return extracted_text, None
                        else:
                            return None, f"Gemini returned empty text for Rule {rule_code}. Finish Reason: {finish_reason}"
                    else:
                        return None, f"No 'parts' found for Rule {rule_code}. Finish Reason: {finish_reason}"
                else:
                    return None, f"No 'candidates' found for Rule {rule_code}. Raw Response: {response_text[:1000]}"

            except json.JSONDecodeError:
                return None, f"Could not parse JSON response for Rule {rule_code}. Raw response: {response_text[:500]}"

        except requests.exceptions.Timeout:
            logging.warning(f"Request timed out for Rule {rule_code} on attempt {attempt + 1}")
            if attempt < MAX_RETRIES:
                time.sleep(RETRY_DELAY_SECONDS)
            else:
                return None, f"Request timed out after {MAX_RETRIES + 1} attempts for Rule {rule_code}"
        except Exception as e:
            logging.error(f"Unexpected error processing Rule {rule_code}: {e}")
            return None, f"Unexpected error: {e}"

    return None, f"Failed to extract Rule {rule_code} after {MAX_RETRIES + 1} attempts."

def test_texas_rules_extraction():
    """Test extraction on Texas rules to validate Gemini 2.5 Pro setup"""
    logging.info(f"Starting Texas Rules TEST extraction using model {GEMINI_MODEL}")
    logging.info(f"Testing with {len(TEST_RULES)} Texas rules")

    # Load PDF
    logging.info(f"Loading local PDF from {PDF_PATH}...")
    try:
        if not os.path.exists(PDF_PATH):
            logging.error(f"PDF file not found at {PDF_PATH}")
            return None
        
        with open(PDF_PATH, 'rb') as pdf_file:
            pdf_data = pdf_file.read()
        
        pdf_stream = io.BytesIO(pdf_data)
        pdf_reader = PdfReader(pdf_stream)
        num_pages = len(pdf_reader.pages)
        logging.info(f"PDF loaded successfully ({num_pages} pages).")
    except Exception as e:
        logging.error(f"Error reading PDF content: {e}")
        return None

    # Process each test rule
    extracted_rules = []
    skipped_rules = {}
    logging.info("Starting Texas Rules TEST extraction process...")

    for i, rule_entry in enumerate(TEST_RULES):
        code = rule_entry['code']
        title = rule_entry['title']
        start_page = rule_entry['start_page']

        logging.info(f"[{i+1}/{len(TEST_RULES)}] TEST Processing Texas Rule {code}: '{title}' (Pages {start_page}-{start_page+2})...")

        # Extract text from first few pages (Texas rules are at the beginning)
        page_texts = []
        try:
            for page_offset in range(3):  # First 3 pages
                page_num_zero_based = start_page - 1 + page_offset
                if 0 <= page_num_zero_based < num_pages:
                    page = pdf_reader.pages[page_num_zero_based]
                    page_text = page.extract_text() or ""
                    if page_text.strip():
                        page_texts.append(f"--- PAGE {page_num_zero_based + 1} ---\n{page_text}")

            if not page_texts:
                logging.warning(f"Rule {code}: Skipped (no text extracted).")
                skipped_rules[code] = "No text extracted"
                continue

            extracted_text, error = extract_rule_text_with_gemini(code, title, page_texts)
            if error:
                logging.error(f"Rule {code}: Failed extraction. Reason: {error}")
                skipped_rules[code] = error
                continue

            extracted_rules.append({
                'code': code,
                'title': title,
                'text': extracted_text
            })
            logging.info(f"Rule {code}: SUCCESS (extracted {len(extracted_text)} chars).")

        except Exception as rule_err:
            logging.error(f"Error processing rule {code}: {rule_err}")
            skipped_rules[code] = f"Unexpected error during processing: {rule_err}"

    # Save test results
    os.makedirs(os.path.dirname(OUTPUT_FILE), exist_ok=True)
    
    output_data = {
        'test_run': True,
        'test_type': 'Texas Rules of Civil Procedure',
        'source_path': PDF_PATH,
        'model_used': GEMINI_MODEL,
        'total_rules_tested': len(TEST_RULES),
        'rules_extracted': len(extracted_rules),
        'rules_skipped': len(skipped_rules),
        'success_rate': f"{len(extracted_rules)}/{len(TEST_RULES)} ({len(extracted_rules)/len(TEST_RULES)*100:.1f}%)",
        'skipped_details': skipped_rules,
        'extracted_rules': extracted_rules
    }

    logging.info(f"\nTEST extraction finished. Writing {len(extracted_rules)} rules to {OUTPUT_FILE}...")
    try:
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=4, ensure_ascii=False)
        logging.info("Successfully wrote TEST results.")
        logging.info(f"SUCCESS RATE: {output_data['success_rate']}")
    except IOError as e:
        logging.error(f"Error writing output file: {e}")

    return OUTPUT_FILE

if __name__ == "__main__":
    test_texas_rules_extraction()
