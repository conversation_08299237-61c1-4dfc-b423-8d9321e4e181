import requests
import json
import os
import time
from dotenv import load_dotenv

load_dotenv() # Load environment variables from .env file

# --- Configuration ---
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-001:generateContent?key={os.getenv('GEMINI_API_KEY')}"
RULES_DIR = os.path.join(os.path.dirname(__file__), '..', 'rules')
TOC_FILENAME = "fl-toc.json"
OCR_FILENAME = "fl-rules-ocr.json"
OUTPUT_FILENAME = "fl-rules-parsed-from-ocr.json"
REQUEST_DELAY_SECONDS = 2 # To avoid hitting API rate limits
HEADERS = {
    'Content-Type': 'application/json',
}
PAGE_CONTEXT_WINDOW = 2 # How many pages including start page to provide as context (e.g., 2 = startPage & startPage+1)

# --- Helper Functions ---
def load_json(filename):
    """Loads JSON data from a file in the rules directory."""
    path = os.path.join(RULES_DIR, filename)
    try:
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File not found - {path}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON from {path}: {e}")
        return None

def isolate_rule_with_gemini(rule_code, rule_title, context_text):
    """Asks Gemini to isolate a specific rule's text from the provided context."""
    if not context_text or context_text.isspace():
        return None, "Context text is empty"

    prompt = (
        f"From the following text extracted from a legal document, please isolate and return ONLY the complete text for Rule {rule_code} titled '{rule_title}'. "
        f"Ensure you capture the entire rule, including all subsections and commentary directly associated with it. "
        f"Exclude any surrounding text belonging to other rules, page numbers, headers, or footers. "
        f"If the rule text is not found in the provided context, return ONLY the text 'RULE_NOT_FOUND'. "
        f"Do not add any explanations or introductory phrases.\n\n"
        f"--- START OF CONTEXT TEXT ---\n"
        f"{context_text[:25000]}" # Limit context to avoid excessive token usage
        f"\n--- END OF CONTEXT TEXT ---"
    )

    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
         "generationConfig": {
             "responseMimeType": "text/plain", # Request plain text output
        }
    }

    try:
        time.sleep(REQUEST_DELAY_SECONDS)
        response = requests.post(GEMINI_API_URL, headers=HEADERS, json=payload, timeout=120)
        response_text = response.text

        if response.status_code != 200:
            return None, f"API Error {response.status_code}: {response_text[:500]}"

        # Parse the Gemini response
        try:
            data = response.json()
            candidates = data.get('candidates', [])
            if candidates:
                content = candidates[0].get('content', {})
                parts = content.get('parts', [])
                if parts:
                    extracted_text = parts[0].get('text', '').strip()
                    if extracted_text == 'RULE_NOT_FOUND':
                        return None, "Rule not found in context by Gemini"
                    elif extracted_text:
                        return extracted_text, None # Success
                    else:
                        finish_reason = candidates[0].get('finishReason', 'UNKNOWN')
                        if finish_reason != 'STOP':
                            return None, f"Gemini returned empty text. Finish Reason: {finish_reason}. Raw Response: {response_text[:500]}"
                        else:
                            return None, "Gemini returned empty text (STOP)"
                else:
                    finish_reason = candidates[0].get('finishReason', 'UNKNOWN')
                    # Check for recitation specifically
                    if finish_reason == 'RECITATION':
                         return None, f"Extraction blocked by RECITATION. Raw Response: {response_text[:1000]}"
                    else:
                        return None, f"No 'parts' found. Finish Reason: {finish_reason}. Raw Response: {response_text[:1000]}"
            else:
                 return None, f"No 'candidates' found. Raw Response: {response_text[:1000]}"

        except json.JSONDecodeError:
             return None, f"Could not parse JSON response. Raw response: {response_text[:500]}"

    except requests.exceptions.RequestException as e:
        return None, f"API Request failed: {e}"
    except Exception as e:
        return None, f"Unexpected error: {e}"

def main():
    # Load required data
    print("Loading OCR data...")
    ocr_data = load_json(OCR_FILENAME)
    if not ocr_data or 'page_content' not in ocr_data:
        print("Failed to load or invalid OCR data format.")
        return
    page_content_map = ocr_data['page_content'] # Page numbers are strings keys in JSON
    total_pdf_pages = ocr_data.get('total_pages', 0)

    print("Loading TOC data...")
    toc_data = load_json(TOC_FILENAME)
    if not toc_data:
        print("Failed to load TOC data.")
        return

    # --- Main Processing Loop ---
    print(f"\nStarting rule isolation for {len(toc_data)} rules...")
    parsed_rules = {}
    skipped_rules = {}

    for i, rule_entry in enumerate(toc_data):
        rule_code = rule_entry.get('code')
        rule_title = rule_entry.get('title', 'N/A')
        start_page = rule_entry.get('startPage')

        if not rule_code or start_page is None:
            print(f"Skipping entry {i+1}: Missing code or startPage.")
            continue

        print(f"\n[{i+1}/{len(toc_data)}] Isolating Rule {rule_code}: '{rule_title}' (Starts on page {start_page})... ", end="")

        # Gather context text from relevant pages
        context_text = ""
        for page_offset in range(PAGE_CONTEXT_WINDOW):
            current_page_num = start_page + page_offset
            # Page numbers in page_content_map are strings from JSON keys
            page_text = page_content_map.get(str(current_page_num))
            if page_text:
                context_text += f"\n\n--- Page {current_page_num} ---\n{page_text}"
            elif current_page_num > total_pdf_pages:
                 break # Don't try to read past the end of the document

        if not context_text.strip():
            print(f"Failed. Reason: No text found for page(s) starting from {start_page}.")
            skipped_rules[rule_code] = f"No text found for page(s) starting from {start_page}"
            continue

        # Isolate rule using Gemini
        isolated_text, error_message = isolate_rule_with_gemini(rule_code, rule_title, context_text)

        if isolated_text:
            parsed_rules[rule_code] = {
                'title': rule_title,
                'text': isolated_text,
                'source_pages': list(range(start_page, start_page + PAGE_CONTEXT_WINDOW))
            }
            print(f"Success (isolated {len(isolated_text)} chars).", end="")
        else:
            print(f"Failed. Reason: {error_message}", end="")
            skipped_rules[rule_code] = error_message

    # --- Save Results ---
    output_path = os.path.join(RULES_DIR, OUTPUT_FILENAME)
    output_data = {
        'total_rules_in_toc': len(toc_data),
        'rules_parsed': len(parsed_rules),
        'rules_skipped': len(skipped_rules),
        'skipped_details': skipped_rules,
        'parsed_rules': parsed_rules
    }

    print(f"\n\nIsolation finished. Writing {len(parsed_rules)} rules to {output_path}...")
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=4, ensure_ascii=False)
        print("Successfully wrote results.")
    except IOError as e:
        print(f"Error writing output file: {e}")

if __name__ == "__main__":
    main()

