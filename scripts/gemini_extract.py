import os
import sys
import json
import requests
import io
import time
from PyPDF2 import PdfReader
from dotenv import load_dotenv
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()

# --- Configuration ---
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    logging.error("Error: GEMINI_API_KEY not found in environment variables.")
    sys.exit(1)

GEMINI_MODEL = 'gemini-2.5-pro-preview-06-05' # Use the more capable Pro model
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/{GEMINI_MODEL}:generateContent?key={GEMINI_API_KEY}"
INPUT_DIR = os.path.join(os.path.dirname(__file__), '../rules')
INPUT_TOC_FILE = os.path.join(INPUT_DIR, 'fl-toc.json')
OUTPUT_RULES_FILE = os.path.join(INPUT_DIR, 'fl-rules-gemini-2.5-pro.json') # Updated filename for new model
REQUEST_DELAY_SECONDS = 3 # Slightly longer delay for Pro model
MAX_RETRIES = 3 # More retries for better success rate
RETRY_DELAY_SECONDS = 10 # Longer retry delay
HEADERS = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
}
PDF_URL = "https://supremecourt.flcourts.gov/content/download/2446888/opinion/Opinion_SC2024-0774.pdf" # Florida Rules of Civil Procedure - April 1, 2025

# --- Helper Functions ---
def extract_rule_text_with_gemini(rule_code, rule_title, page_texts):
    """Sends relevant page text to Gemini and asks for the specific rule's text."""
    if not page_texts:
        return None, "No page text provided"

    # Combine page texts with separators for clarity
    context_text = "\n\n---\n\n".join(page_texts)

    # Enhanced prompt for better rule extraction with Pro model
    prompt = (
        f"You are extracting legal rules from the Florida Rules of Civil Procedure PDF. "
        f"This is a publicly available legal document. Your task is to find and extract the complete text of a specific rule.\n\n"
        f"TARGET RULE:\n"
        f"- Rule Code: {rule_code}\n"
        f"- Rule Title: {rule_title}\n\n"
        f"INSTRUCTIONS:\n"
        f"1. Look for the rule that starts with 'RULE {rule_code}' or similar formatting\n"
        f"2. Extract the complete rule text including:\n"
        f"   - The rule header (RULE {rule_code}. {rule_title})\n"
        f"   - All subsections (a), (b), (c), etc.\n"
        f"   - All sub-subsections (1), (2), (3), etc.\n"
        f"   - Any commentary, notes, or amendments\n"
        f"   - Stop when you reach the next rule or end of the rule\n"
        f"3. If you cannot find the rule clearly, return exactly: RULE_NOT_FOUND\n"
        f"4. Return ONLY the rule text, no additional commentary\n\n"
        f"CONTEXT TEXT FROM PDF:\n"
        f"--- START ---\n"
        f"{context_text[:50000]}" # Increased context for Pro model
        f"\n--- END ---"
    )

    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
            "responseMimeType": "text/plain",
            "temperature": 0.1,  # Low temperature for consistent extraction
            "maxOutputTokens": 8192,  # Increased for longer rules
            "topP": 0.8,
            "topK": 10
        }
    }

    for attempt in range(MAX_RETRIES + 1):
        try:
            logging.debug(f"Attempt {attempt + 1} for Rule {rule_code}")
            time.sleep(REQUEST_DELAY_SECONDS)
            response = requests.post(GEMINI_API_URL, headers=HEADERS, json=payload, timeout=300) # Extended timeout for Pro model
            response_text = response.text

            if response.status_code == 429: # Rate limiting
                logging.warning(f"Rate limit hit for Rule {rule_code}. Retrying in {RETRY_DELAY_SECONDS}s...")
                time.sleep(RETRY_DELAY_SECONDS)
                continue
            elif response.status_code != 200:
                return None, f"API Error {response.status_code}: {response_text[:500]}"

            # Parse the Gemini response
            try:
                data = response.json()
                candidates = data.get('candidates', [])
                if candidates:
                    content = candidates[0].get('content', {})
                    parts = content.get('parts', [])
                    finish_reason = candidates[0].get('finishReason', 'UNKNOWN')

                    if parts:
                        extracted_text = parts[0].get('text', '').strip()
                        if extracted_text == 'RULE_NOT_FOUND':
                            return None, f"Rule {rule_code} not found in context by Gemini (finishReason: {finish_reason})"
                        elif extracted_text:
                             logging.debug(f"Rule {rule_code} extracted successfully (finishReason: {finish_reason})")
                             return extracted_text, None # Success
                        else: # Empty text returned
                             if finish_reason == 'STOP':
                                 # This might happen if the rule truly is empty or model couldn't find it
                                 return None, f"Gemini returned empty text for Rule {rule_code} (finishReason: STOP)"
                             else:
                                 # Other reasons for empty text are usually errors
                                 return None, f"Gemini returned empty text for Rule {rule_code}. Finish Reason: {finish_reason}. Raw: {response_text[:500]}"
                    else: # No 'parts' found
                        # Check specific finish reasons
                        if finish_reason == 'RECITATION':
                             logging.warning(f"Extraction blocked for Rule {rule_code} due to RECITATION.")
                             return None, f"Extraction blocked by RECITATION. Raw Response: {response_text[:1000]}"
                        elif finish_reason == 'SAFETY':
                            logging.warning(f"Extraction blocked for Rule {rule_code} due to SAFETY.")
                            return None, f"Extraction blocked by SAFETY. Raw Response: {response_text[:1000]}"
                        else:
                            return None, f"No 'parts' found for Rule {rule_code}. Finish Reason: {finish_reason}. Raw Response: {response_text[:1000]}"
                else: # No 'candidates' found
                     return None, f"No 'candidates' found for Rule {rule_code}. Raw Response: {response_text[:1000]}"

            except json.JSONDecodeError:
                 return None, f"Could not parse JSON response for Rule {rule_code}. Raw response: {response_text[:500]}"

        except requests.exceptions.Timeout:
            logging.warning(f"Request timed out for Rule {rule_code} on attempt {attempt + 1}")
            if attempt < MAX_RETRIES:
                time.sleep(RETRY_DELAY_SECONDS)
            else:
                return None, f"Request timed out after {MAX_RETRIES + 1} attempts for Rule {rule_code}"
        except requests.exceptions.RequestException as e:
            logging.error(f"API Request failed for Rule {rule_code}: {e}")
            return None, f"API Request failed: {e}"
        except Exception as e:
            logging.error(f"Unexpected error processing Rule {rule_code}: {e}")
            return None, f"Unexpected error: {e}"

    return None, f"Failed to extract Rule {rule_code} after {MAX_RETRIES + 1} attempts."

def extract_rules(pdf_url):
    """Reads TOC, fetches PDF, extracts rule text using Gemini, saves to JSON."""
    # Check if pdf_url is None or empty, and log an error if it is
    if not pdf_url:
        logging.error("PDF URL is missing or empty. Cannot proceed.")
        return None
    logging.info(f"Starting rule extraction from {pdf_url} using model {GEMINI_MODEL}")

    # --- Load TOC ---
    logging.info(f"Loading TOC from {INPUT_TOC_FILE}...")
    try:
        with open(INPUT_TOC_FILE, 'r', encoding='utf-8') as f:
            toc_data = json.load(f)
        logging.info(f"Successfully loaded {len(toc_data)} rules from TOC.")
    except FileNotFoundError:
        logging.error(f"Error: TOC file not found at {INPUT_TOC_FILE}")
        return None
    except json.JSONDecodeError as e:
        logging.error(f"Error decoding JSON from {INPUT_TOC_FILE}: {e}")
        return None

    # --- Fetch and Read PDF --- (Assuming PyPDF2 is still suitable here)
    logging.info("Fetching PDF...")
    try:
        response = requests.get(pdf_url, headers=HEADERS, stream=True, timeout=60)
        response.raise_for_status()
        pdf_stream = io.BytesIO(response.content)
        pdf_reader = PdfReader(pdf_stream)
        num_pages = len(pdf_reader.pages)
        logging.info(f"PDF downloaded and opened ({num_pages} pages).")
    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching PDF: {e}")
        return None
    except Exception as e:
        logging.error(f"Error reading PDF content: {e}")
        return None

    # --- Process Each Rule ---
    extracted_rules = []
    skipped_rules = {}
    logging.info("Starting extraction process for each rule...")

    for i, rule_entry in enumerate(toc_data):
        code = rule_entry.get('code')
        title = rule_entry.get('title')
        start_page = rule_entry.get('startPage')
        end_page = rule_entry.get('endPage') # We'll ignore this for context now

        if not code or not title or start_page is None:
            logging.warning(f"Skipping entry {i+1}: Missing data (code={code}, title={title}, start={start_page})")
            continue

        # Determine page range for extraction - use end_page if available, otherwise expand context
        if end_page and end_page > start_page:
            # Use the actual range from TOC
            page_range = min(end_page - start_page + 1, 5)  # Cap at 5 pages max
            context_desc = f"Pages {start_page}-{end_page}"
        else:
            # Default to 3 pages for better context
            page_range = 3
            context_desc = f"Pages {start_page}-{start_page+2}"

        logging.info(f"[{i+1}/{len(toc_data)}] Processing Rule {code}: '{title}' (Context: {context_desc})...")

        # Extract text from relevant pages with expanded context
        page_texts = []
        try:
            for page_index_offset in range(page_range):
                page_num_zero_based = start_page - 1 + page_index_offset
                if 0 <= page_num_zero_based < num_pages:
                    page = pdf_reader.pages[page_num_zero_based]
                    page_text = page.extract_text() or ""
                    if page_text.strip():  # Only add non-empty pages
                        page_texts.append(f"--- PAGE {page_num_zero_based + 1} ---\n{page_text}")

            if not page_texts:
                logging.warning(f"Rule {code}: Skipped (no text extracted from {context_desc}).")
                skipped_rules[code] = f"No text extracted from {context_desc}"
                continue

            extracted_text, error = extract_rule_text_with_gemini(code, title, page_texts) # Pass list of page texts
            if error:
                logging.error(f"Rule {code}: Failed extraction. Reason: {error}")
                skipped_rules[code] = error
                continue

            # Basic check on extracted text (e.g., minimum length)
            if len(extracted_text) < 10:
                 logging.warning(f"Rule {code}: Extracted text seems very short ({len(extracted_text)} chars). Adding anyway.")

            extracted_rules.append({
                'code': code,
                'title': title,
                'text': extracted_text
            })
            logging.info(f"Rule {code}: Success (extracted {len(extracted_text)} chars).")

        except Exception as rule_err:
            logging.error(f"Error processing rule {code}: {rule_err}")
            skipped_rules[code] = f"Unexpected error during processing: {rule_err}"

    # --- Save Results ---
    # Create output directory if it doesn't exist
    os.makedirs(INPUT_DIR, exist_ok=True) # Use INPUT_DIR here
    output_path = OUTPUT_RULES_FILE # Use the defined output file variable

    output_data = {
        'source_url': pdf_url,
        'model_used': GEMINI_MODEL, # Record the model used
        'total_rules_in_toc': len(toc_data),
        'rules_extracted': len(extracted_rules),
        'rules_skipped': len(skipped_rules),
        'skipped_details': skipped_rules,
        'extracted_rules': extracted_rules
    }

    logging.info(f"\nExtraction finished. Writing {len(extracted_rules)} rules to {output_path}...")
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=4, ensure_ascii=False)
        logging.info("Successfully wrote results.")
    except IOError as e:
        logging.error(f"Error writing output file: {e}")

    return output_path

if __name__ == "__main__":
    # Use the globally defined PDF_URL
    extract_rules(PDF_URL)
