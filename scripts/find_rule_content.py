#!/usr/bin/env python3
"""
Find where actual rule content starts in the PDF
"""

import os
import io
from PyPDF2 import PdfReader

PDF_PATH = os.path.join(os.path.dirname(__file__), '../test/fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.pdf')

def find_rule_content():
    """Find where actual rule content starts"""
    print(f"Loading PDF from {PDF_PATH}...")
    
    try:
        with open(PDF_PATH, 'rb') as pdf_file:
            pdf_data = pdf_file.read()
        
        pdf_stream = io.BytesIO(pdf_data)
        pdf_reader = PdfReader(pdf_stream)
        num_pages = len(pdf_reader.pages)
        print(f"PDF loaded successfully ({num_pages} pages).")
        
        # Search for actual rule content by looking for specific patterns
        found_content = False
        for page_num in range(min(100, num_pages)):  # Check first 100 pages
            page = pdf_reader.pages[page_num]
            page_text = page.extract_text() or ""

            # Look for actual rule content patterns
            content_indicators = [
                "These rules shall be construed",
                "OBJECTIVE OF RULES",
                "The objective of these rules",
                "PART I",
                "GENERAL RULES"
            ]

            # Skip table of contents pages
            if "Table of Contents" in page_text:
                continue

            # Look for pages with substantial rule content
            if any(indicator in page_text for indicator in content_indicators) and len(page_text) > 200:
                print(f"\n{'='*60}")
                print(f"FOUND POTENTIAL RULE CONTENT ON PAGE {page_num + 1}")
                print(f"{'='*60}")
                print(page_text[:3000])
                if len(page_text) > 3000:
                    print(f"\n... (truncated, total length: {len(page_text)} chars)")
                found_content = True

                # Also check next page
                if page_num + 1 < num_pages:
                    next_page = pdf_reader.pages[page_num + 1]
                    next_text = next_page.extract_text() or ""
                    print(f"\n{'='*60}")
                    print(f"NEXT PAGE {page_num + 2}")
                    print(f"{'='*60}")
                    print(next_text[:2000])
                break

        if not found_content:
            print("Could not find actual rule content in first 100 pages")
            # Show a few more pages to understand structure
            for page_num in range(10, min(20, num_pages)):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text() or ""
                print(f"\n--- PAGE {page_num + 1} (sample) ---")
                print(page_text[:500])
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    find_rule_content()
