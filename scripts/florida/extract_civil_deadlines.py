#!/usr/bin/env python3
"""
Florida Civil Procedure Deadline Extraction Script
Extracts deadline-related rules from Florida Rules of Civil Procedure for Personal Injury cases
"""

import re
import yaml
from datetime import datetime

def extract_deadline_rules(text_file_path, output_yaml_path):
    """Extract deadline rules from Florida Civil Procedure text"""
    
    with open(text_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define deadline-related rules to extract
    deadline_rules = {
        'RULE_1_090_TIME': {
            'title': 'Time Computation and Extensions',
            'pattern': r'RULE 1\.090\..*?TIME.*?(?=RULE 1\.1)',
            'category': 'time_computation'
        },
        'RULE_1_140_DEFENSES': {
            'title': 'Defense Response Deadlines',
            'pattern': r'RULE 1\.140\..*?DEFENSES.*?(?=RULE 1\.15)',
            'category': 'pleading_deadlines'
        },
        'RULE_1_070_PROCESS': {
            'title': 'Service of Process Deadlines',
            'pattern': r'RULE 1\.070\..*?PROCESS.*?(?=RULE 1\.071)',
            'category': 'service_deadlines'
        },
        'RULE_1_280_DISCOVERY': {
            'title': 'Discovery Deadlines',
            'pattern': r'RULE 1\.280\..*?DISCOVERY.*?(?=RULE 1\.285)',
            'category': 'discovery_deadlines'
        },
        'RULE_1_420_DISMISSAL': {
            'title': 'Dismissal for Lack of Prosecution',
            'pattern': r'RULE 1\.420\..*?DISMISSAL.*?(?=RULE 1\.430)',
            'category': 'case_management'
        }
    }
    
    extracted_rules = {}
    
    for rule_key, rule_info in deadline_rules.items():
        match = re.search(rule_info['pattern'], content, re.DOTALL | re.IGNORECASE)
        if match:
            rule_text = match.group(0)
            
            # Extract specific deadlines from the rule text
            deadlines = extract_specific_deadlines(rule_text)
            
            extracted_rules[rule_key] = {
                'title': rule_info['title'],
                'category': rule_info['category'],
                'full_text': rule_text[:1000] + '...' if len(rule_text) > 1000 else rule_text,
                'deadlines': deadlines,
                'extracted_date': datetime.now().isoformat()
            }
    
    # Create YAML structure for MCP system
    florida_civil_deadlines = {
        'jurisdiction': 'Florida',
        'court_type': 'Civil',
        'practice_area': 'Personal Injury',
        'last_updated': '2025-06-05',
        'source': 'Florida Rules of Civil Procedure',
        'rules': extracted_rules
    }
    
    # Save to YAML file
    with open(output_yaml_path, 'w', encoding='utf-8') as f:
        yaml.dump(florida_civil_deadlines, f, default_flow_style=False, sort_keys=False)
    
    return florida_civil_deadlines

def extract_specific_deadlines(rule_text):
    """Extract specific deadline patterns from rule text"""
    deadlines = []
    
    # Common deadline patterns
    patterns = [
        (r'(\d+)\s+days?(?:\s+after|from)', 'days_after'),
        (r'within\s+(\d+)\s+days?', 'within_days'),
        (r'(\d+)\s+months?', 'months'),
        (r'(\d+)\s+years?', 'years'),
        (r'120\s+days?', 'service_deadline'),
        (r'20\s+days?', 'response_deadline'),
        (r'30\s+days?', 'extended_deadline'),
        (r'40\s+days?', 'state_response'),
        (r'60\s+days?', 'motion_deadline'),
        (r'10\s+days?', 'short_deadline')
    ]
    
    for pattern, deadline_type in patterns:
        matches = re.finditer(pattern, rule_text, re.IGNORECASE)
        for match in matches:
            context_start = max(0, match.start() - 100)
            context_end = min(len(rule_text), match.end() + 100)
            context = rule_text[context_start:context_end].strip()
            
            deadlines.append({
                'type': deadline_type,
                'value': match.group(1) if match.groups() else match.group(0),
                'context': context,
                'full_match': match.group(0)
            })
    
    return deadlines

if __name__ == "__main__":
    # Extract deadlines from civil procedure text
    input_file = "rules/florida/extracted_text/civil/civil_procedure_raw.txt"
    output_file = "rules/florida/processed/civil_deadlines.yaml"
    
    print("Extracting Florida Civil Procedure deadlines...")
    result = extract_deadline_rules(input_file, output_file)
    
    print(f"Extracted {len(result['rules'])} deadline rules")
    print(f"Saved to: {output_file}")
    
    # Print summary
    for rule_key, rule_data in result['rules'].items():
        print(f"\n{rule_key}: {rule_data['title']}")
        print(f"  Category: {rule_data['category']}")
        print(f"  Deadlines found: {len(rule_data['deadlines'])}")
