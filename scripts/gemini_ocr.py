import requests
import json
import os
import time
from PyPDF2 import PdfReader
from io import BytesIO
from dotenv import load_dotenv

load_dotenv() # Load environment variables from .env file

# --- Configuration ---
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-001:generateContent?key={os.getenv('GEMINI_API_KEY')}"
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), '..', 'rules')
OUTPUT_FILENAME = "fl-rules-ocr.json"
REQUEST_DELAY_SECONDS = 2 # To avoid hitting API rate limits
HEADERS = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36' # Add User-Agent
}

# --- Helper Functions ---
def fetch_pdf(url):
    print(f"Fetching PDF from {url}...")
    try:
        response = requests.get(url, headers=HEADERS, stream=True, timeout=60)
        response.raise_for_status()
        print("PDF downloaded. Reading content...")
        return BytesIO(response.content)
    except requests.exceptions.RequestException as e:
        print(f"Error fetching PDF: {e}")
        return None

def extract_text_from_page_gemini(page_number, page_text):
    """Sends page text to Gemini and asks for OCR/clean text extraction."""
    if not page_text or page_text.isspace():
        return None, "Skipped empty page"

    prompt = (
        f"Perform OCR / text extraction on the following text content from page {page_number+1} of a legal document. "
        f"Extract all text as accurately as possible. Preserve line breaks and basic structure where feasible. "
        f"Do not summarize, interpret, or add any information not present in the input text. "
        f"Only return the extracted text, nothing else.\n\n"
        f"--- START OF PAGE {page_number+1} TEXT ---\n"
        f"{page_text[:15000]}"
        f"\n--- END OF PAGE {page_number+1} TEXT ---"
    )

    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
             "responseMimeType": "text/plain", # Request plain text output
        }
    }

    try:
        time.sleep(REQUEST_DELAY_SECONDS)
        response = requests.post(GEMINI_API_URL, headers=HEADERS, json=payload, timeout=90)
        response_text = response.text

        if response.status_code != 200:
            return None, f"API Error {response.status_code}: {response_text[:500]}"

        # Parse the Gemini response (which should now directly be text/plain)
        # The structure is slightly different for text/plain
        try:
            data = response.json()
            candidates = data.get('candidates', [])
            if candidates:
                content = candidates[0].get('content', {})
                parts = content.get('parts', [])
                if parts:
                    extracted_text = parts[0].get('text', '').strip()
                    if extracted_text:
                        return extracted_text, None # Success
                    else:
                         # Check finish reason if no text
                        finish_reason = candidates[0].get('finishReason', 'UNKNOWN')
                        if finish_reason != 'STOP': # STOP is normal for empty text
                            return None, f"Gemini returned empty text. Finish Reason: {finish_reason}. Raw Response: {response_text[:500]}"
                        else:
                            return "", None # Treat STOP on empty result as empty page
                else:
                    finish_reason = candidates[0].get('finishReason', 'UNKNOWN')
                    return None, f"No 'parts' found. Finish Reason: {finish_reason}. Raw Response: {response_text[:1000]}"
            else:
                 return None, f"No 'candidates' found. Raw Response: {response_text[:1000]}"

        except json.JSONDecodeError:
             return None, f"Could not parse JSON response. Raw response: {response_text[:500]}"

    except requests.exceptions.RequestException as e:
        return None, f"API Request failed: {e}"
    except Exception as e:
        return None, f"Unexpected error: {e}"


def main(pdf_url):
    pdf_data = fetch_pdf(pdf_url)
    if not pdf_data:
        return

    try:
        pdf_reader = PdfReader(pdf_data)
        num_pages = len(pdf_reader.pages)
        print(f"PDF loaded ({num_pages} pages). Starting page-by-page OCR...")
    except Exception as e:
        print(f"Error reading PDF content: {e}")
        return

    ocr_results = {}
    errors = {}

    for i in range(num_pages):
        print(f"Processing page {i+1}/{num_pages}... ", end="")
        try:
            page = pdf_reader.pages[i]
            page_text_pypdf = page.extract_text()
        except Exception as e:
            print(f"Failed (PyPDF2 error: {e})")
            errors[i+1] = f"PyPDF2 error: {e}"
            continue

        extracted_text, error_message = extract_text_from_page_gemini(i, page_text_pypdf)

        if extracted_text is not None: # Includes empty string for intentionally empty pages
            ocr_results[i+1] = extracted_text
            print(f"Success (extracted {len(extracted_text)} chars).")
        else:
            print(f"Failed. Reason: {error_message}")
            errors[i+1] = error_message

    # Create output directory if it doesn't exist
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    output_path = os.path.join(OUTPUT_DIR, OUTPUT_FILENAME)

    # Save results
    output_data = {
        'source_url': pdf_url,
        'total_pages': num_pages,
        'pages_processed': len(ocr_results) + len(errors),
        'pages_successful': len(ocr_results),
        'pages_failed': len(errors),
        'page_errors': errors,
        'page_content': ocr_results # Dict: {page_num: text}
    }

    print(f"\nOCR finished. Writing {len(ocr_results)} pages to {output_path}...")
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=4, ensure_ascii=False)
        print("Successfully wrote results.")
    except IOError as e:
        print(f"Error writing output file: {e}")

if __name__ == "__main__":
    # Check if URL is provided as command-line argument
    import sys
    if len(sys.argv) > 1:
        pdf_url_arg = sys.argv[1]
        main(pdf_url_arg)
    else:
        print("Usage: python gemini_ocr.py <pdf_url>")
