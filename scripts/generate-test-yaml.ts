import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf.mjs';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { convertTextToYaml } from './convertTextToYaml.js';

// Define __filename and __dirname first
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Required setup for pdfjs-dist in Node.js
// Point workerSrc to the legacy worker build
try {
    // Construct the path relative to the current script's location
    const workerPath = path.resolve(
        __dirname,
        '../node_modules/pdfjs-dist/legacy/build/pdf.worker.mjs' // Assuming standard node_modules structure
    );

    // Check if the file exists before assigning
    await fs.access(workerPath);

    // @ts-ignore - workerSrc assignment might not have perfect types
    pdfjsLib.GlobalWorkerOptions.workerSrc = workerPath;
    console.log('Using pdfjs-dist legacy worker:', pdfjsLib.GlobalWorkerOptions.workerSrc);

} catch (e) {
     console.error(`Failed to find or access the pdf.js legacy worker.
     Attempted path: ${path.resolve(__dirname, '../node_modules/pdfjs-dist/legacy/build/pdf.worker.mjs')}
     Error: ${e}.
     PDF processing will likely fail. Check the path relative to node_modules.`);
    // We might attempt alternative paths like before, but let's see if the direct import works first
    // Consider adding the pnpm-specific path check here if the standard one fails
    process.exit(1); // Exit if worker setup fails
}


// Derive remaining paths relative to this script
const baseDir = path.resolve(__dirname, '..'); // Project root

const pdfFixturePath = path.join(baseDir, 'test/fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.pdf');
const generatedYamlPath = path.join(baseDir, 'test/fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.generated.yaml');
const rawTextOutputPath = path.join(baseDir, 'test/fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.raw_text.txt');

async function getTextFromPdf(pdfPath: string): Promise<string> {
    const data = new Uint8Array(await fs.readFile(pdfPath));
    const loadingTask = pdfjsLib.getDocument({
        data,
        // Try disabling font loading if it causes issues in Node
        // disableFontFace: true
    });
    const pdf = await loadingTask.promise;
    let fullText = '';

    for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        // Ensure items exist before mapping
        // Join with newline ('\n') to preserve line structure better for parsing
        const pageText = textContent.items ? textContent.items.map(item => 'str' in item ? item.str : '').join('\n') : ''; 
        fullText += pageText + '\n\n--- Page Break ---\n\n'; // Keep page break marker for now
        // Clean up page resources to conserve memory, important for large PDFs
        page.cleanup();
    }
    // Clean up document resources
    pdf.destroy();
    return fullText;
}

async function generateYaml() {
  try {
    console.log('Reading PDF fixture: ' + pdfFixturePath);
    const pdfText = await getTextFromPdf(pdfFixturePath);

    console.log('Writing raw extracted text to: ' + rawTextOutputPath);
    await fs.writeFile(rawTextOutputPath, pdfText, 'utf8');

    console.log('Generating YAML from PDF text...');
    const yamlStr = convertTextToYaml(pdfText, 'tx-civil');

    console.log('Writing generated YAML to: ' + generatedYamlPath);
    await fs.writeFile(generatedYamlPath, yamlStr, 'utf8');

    console.log('Successfully generated test YAML fixture and raw text file.');
  } catch (error) {
    console.error('Error generating test YAML:', error);
    process.exit(1); // Exit with error code if generation fails
  }
}

generateYaml();
