#!/usr/bin/env python3
"""
Examine the Florida PDF to understand its structure
"""

import os
import io
import requests
from PyPDF2 import PdfReader

PDF_URL = "https://supremecourt.flcourts.gov/content/download/2446888/opinion/Opinion_SC2024-0774.pdf"
HEADERS = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
}

def examine_florida_pdf():
    """Examine the Florida PDF structure"""
    print(f"Fetching Florida PDF from {PDF_URL}...")
    
    try:
        response = requests.get(PDF_URL, headers=HEADERS, stream=True, timeout=60)
        response.raise_for_status()
        pdf_stream = io.BytesIO(response.content)
        pdf_reader = PdfReader(pdf_stream)
        num_pages = len(pdf_reader.pages)
        print(f"PDF downloaded and opened ({num_pages} pages).")
        
        # Extract first few pages to understand structure
        for page_num in range(min(5, num_pages)):
            print(f"\n{'='*60}")
            print(f"PAGE {page_num + 1}")
            print(f"{'='*60}")
            
            page = pdf_reader.pages[page_num]
            page_text = page.extract_text() or ""
            
            # Show first 2000 characters of each page
            print(page_text[:2000])
            if len(page_text) > 2000:
                print(f"\n... (truncated, total length: {len(page_text)} chars)")
                
        # Also check if there are any pages with "RULE 1.010" or similar
        print(f"\n{'='*60}")
        print("SEARCHING FOR RULE CONTENT")
        print(f"{'='*60}")
        
        rules_found = []
        for page_num in range(num_pages):
            page = pdf_reader.pages[page_num]
            page_text = page.extract_text() or ""
            
            # Look for rule patterns
            if "RULE 1." in page_text:
                lines = page_text.split('\n')
                for line in lines:
                    if "RULE 1." in line:
                        rules_found.append(f"Page {page_num + 1}: {line.strip()}")
        
        if rules_found:
            print("Found rule references:")
            for rule in rules_found[:10]:  # Show first 10
                print(f"  {rule}")
        else:
            print("No RULE 1.xxx patterns found")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    examine_florida_pdf()
