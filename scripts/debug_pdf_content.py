#!/usr/bin/env python3
"""
Debug script to examine PDF content and understand structure
"""

import os
import io
from PyPDF2 import PdfReader

PDF_PATH = os.path.join(os.path.dirname(__file__), '../test/fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.pdf')

def debug_pdf_content():
    """Extract and examine first few pages of PDF"""
    print(f"Loading PDF from {PDF_PATH}...")
    
    try:
        with open(PDF_PATH, 'rb') as pdf_file:
            pdf_data = pdf_file.read()
        
        pdf_stream = io.BytesIO(pdf_data)
        pdf_reader = PdfReader(pdf_stream)
        num_pages = len(pdf_reader.pages)
        print(f"PDF loaded successfully ({num_pages} pages).")
        
        # Extract first 5 pages to see structure
        for page_num in range(min(5, num_pages)):
            print(f"\n{'='*50}")
            print(f"PAGE {page_num + 1}")
            print(f"{'='*50}")
            
            page = pdf_reader.pages[page_num]
            page_text = page.extract_text() or ""
            
            # Show first 2000 characters of each page
            print(page_text[:2000])
            if len(page_text) > 2000:
                print(f"\n... (truncated, total length: {len(page_text)} chars)")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    debug_pdf_content()
