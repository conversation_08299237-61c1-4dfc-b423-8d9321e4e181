import { calculateDeadlines } from "../src/engine/calculator";
import { loadHolidays } from "../src/loaders/holidayLoader"; 
import { loadRules } from '../src/loaders/ruleLoader';

describe("Deadline Calculator", () => {
  // Load holidays once before tests run
  beforeAll(async () => {
    await loadHolidays(2025); // Pass the year for which holidays are needed for the tests
    await loadRules('TX_STATE'); // Load rules needed for the tests
  });

  it("rolls forward on holiday", () => {
    const res = calculateDeadlines({
      jurisdiction: "TX_STATE",
      triggerCode: "SERVICE_OF_PROCESS",
      startDate: "2025-04-01"
    });
    // Ensure the result array is not empty before accessing index 0
    expect(res.length).toBeGreaterThan(0);
    expect(res[0].deadlineDate).toBe("2025-04-22"); // 20 + roll
  });
});
