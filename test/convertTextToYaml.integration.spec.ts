import yaml from 'js-yaml';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

// jest.setTimeout(300000); // Temporarily comment out to see if describe/it run

describe('convertTextToYaml integration with fixture', () => {
  it('should produce YAML matching the expected structure', async () => {
    // Use import.meta.url for ESM-compatible path resolution
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);

    // Paths to the pre-generated and expected YAML files
    const generatedYamlPath = path.join(__dirname, 'fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.generated.yaml');
    const expectedYamlPath = path.join(__dirname, 'fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.expected.yaml');

    // 1. Read pre-generated YAML (created by generate-test-yaml.ts)
    const generatedYamlStr = await fs.readFile(generatedYamlPath, 'utf8');
    const generatedObj = yaml.load(generatedYamlStr) as any;

    // 2. Read expected YAML fixture
    const expectedYamlStr = await fs.readFile(expectedYamlPath, 'utf8');
    const expectedObj = yaml.load(expectedYamlStr) as any;

    // 3. Compare generated vs expected
    expect(generatedObj).toBeDefined();
    // Use JSON.stringify for potentially better diff output on failure
    expect(JSON.stringify(generatedObj, null, 2)).toEqual(JSON.stringify(expectedObj, null, 2));
    // expect(generatedObj).toEqual(expectedObj); // Keep this as an alternative comparison
  });
});
