import { convertTextToYaml } from '../scripts/convertTextToYaml';
import yaml from 'js-yaml';

describe('convertTextToYaml unit', () => {
  it('should parse multiple rules into YAML structure', () => {
    const sampleText = `Rule 1. First Rule\nThis is the first rule text.\nDetails line.\n\nRule 2. Second Rule\nSecond rule content here.`;
    const result = convertTextToYaml(sampleText, 'tx-civil');
    const obj = yaml.load(result) as any;
    expect(obj.meta.jurisdiction).toBe('tx-civil');
    expect(Array.isArray(obj.rules)).toBe(true);
    expect(obj.rules.length).toBe(2);
    expect(obj.rules[0].code).toBe('RULE_1');
    expect(obj.rules[0].title).toBe('First Rule');
    expect(obj.rules[0].description).toContain('This is the first rule text.');
    expect(obj.rules[1].code).toBe('RULE_2');
    expect(obj.rules[1].title).toBe('Second Rule');
  });
});
