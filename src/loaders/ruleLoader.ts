import { readdirSync, readFileSync } from "fs";
import { join } from "path";
import YAM<PERSON> from "yaml";
import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';
import { loadHolidays } from './holidayLoader.js'; // Import loadHolidays

const RULES: Record<string, any> = {};

// Accepts optional jurisdiction. Loads specific file if provided, otherwise all.
export async function loadRules(jurisdiction?: string): Promise<void> {
  const rulesDir = join(process.cwd(), "rules");
  const files = readdirSync(rulesDir);

  const yamlFiles = files.filter(f => f.endsWith(".yaml"));

  if (jurisdiction) {
    // Find the file matching the jurisdiction
    const targetFile = yamlFiles.find(f => {
      try {
        const content = readFileSync(join(rulesDir, f), "utf8");
        const doc = YAML.parse(content);
        return doc?.meta?.jurisdiction === jurisdiction;
      } catch (e) {
        console.error(`Error parsing ${f}:`, e);
        return false;
      }
    });

    if (targetFile) {
      console.log(`Loading rules for specific jurisdiction: ${jurisdiction} from ${targetFile}`);
      try {
        const content = readFileSync(join(rulesDir, targetFile), "utf8");
        const doc = YAML.parse(content);
        RULES[jurisdiction] = doc;
      } catch (e) {
        console.error(`Error loading specific rule file ${targetFile}:`, e);
      }
    } else {
      console.warn(`No rule file found for jurisdiction: ${jurisdiction}`);
    }
  } else {
    // Load all files if no specific jurisdiction is given
    console.log(`Loading all rule files from ${rulesDir}...`);
    yamlFiles.forEach(f => {
      try {
        const content = readFileSync(join(rulesDir, f), "utf8");
        const doc = YAML.parse(content);
        if (doc?.meta?.jurisdiction) {
            RULES[doc.meta.jurisdiction] = doc;
        } else {
            console.warn(`Rule file ${f} is missing meta.jurisdiction`);
        }
      } catch (e) {
        console.error(`Error parsing ${f}:`, e);
      }
    });
    console.log(`Loaded rules for ${Object.keys(RULES).length} jurisdictions (all).`);
  }
}

export const getRules = (jur: string) => RULES[jur];

// Helper to check if rules for a specific jurisdiction are loaded
export const areRulesLoaded = (jur: string): boolean => {
    return !!RULES[jur];
};

export function getRule(jur: string, triggerCode: string) {
  const pack = RULES[jur]; // Assume loadRules has already populated this
  if (!pack || !pack.triggers) {
    // This case implies rules for the jurisdiction itself weren't loaded/found
    throw new Error(`Rules not found or improperly structured for jurisdiction: ${jur}`);
  }
  
  const trigger = pack.triggers.find((t: any) => t.code === triggerCode);
  
  if (!trigger) {
    // Trigger code specifically not found within the loaded jurisdiction rules
    throw new Error(`Trigger code '${triggerCode}' not found for jurisdiction: ${jur}`);
  }
  
  return trigger;
}

// Ensure rules are loaded at startup
loadRules().catch(err => console.error('Failed to load rules:', err));

// Preload holidays for the current and next year on startup
const currentYear = new Date().getFullYear();
loadHolidays(currentYear)
  .then(() => console.log(`Holiday cache preloaded for ${currentYear}`))
  .catch(err => console.error(`Failed to preload holidays for ${currentYear}:`, err));
loadHolidays(currentYear + 1)
  .then(() => console.log(`Holiday cache preloaded for ${currentYear + 1}`))
  .catch(err => console.error(`Failed to preload holidays for ${currentYear + 1}:`, err));
