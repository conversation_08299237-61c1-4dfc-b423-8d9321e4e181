import fetch from "node-fetch";

// Cache structure: Jurisdiction -> Year -> Set of holiday dates (YYYY-MM-DD)
let HOLIDAYS: Record<string, Record<number, Set<string>>> = {};

// Define the expected structure of a holiday object from the Nager API
interface NagerHoliday {
  date: string; // YYYY-MM-DD
  localName: string;
  name: string;
  countryCode: string;
  fixed: boolean;
  global: boolean;
  counties: string[] | null;
  launchYear: number | null;
  types: string[];
}

// Map internal jurisdiction codes to Nager API state codes (used for filtering)
const jurisdictionToNagerCode: Record<string, string> = {
  AL_STATE: "US-AL",
  AK_STATE: "US-AK",
  AZ_STATE: "US-AZ",
  AR_STATE: "US-AR",
  CA_STATE: "US-CA",
  CO_STATE: "US-CO",
  CT_STATE: "US-CT",
  DE_STATE: "US-DE",
  FL_STATE: "US-FL",
  GA_STATE: "US-GA",
  HI_STATE: "US-HI",
  ID_STATE: "US-ID",
  IL_STATE: "US-IL",
  IN_STATE: "US-IN",
  IA_STATE: "US-IA",
  KS_STATE: "US-KS",
  KY_STATE: "US-KY",
  LA_STATE: "US-LA",
  ME_STATE: "US-ME",
  MD_STATE: "US-MD",
  MA_STATE: "US-MA",
  MI_STATE: "US-MI",
  MN_STATE: "US-MN",
  MS_STATE: "US-MS",
  MO_STATE: "US-MO",
  MT_STATE: "US-MT",
  NE_STATE: "US-NE",
  NV_STATE: "US-NV",
  NH_STATE: "US-NH",
  NJ_STATE: "US-NJ",
  NM_STATE: "US-NM",
  NY_STATE: "US-NY",
  NC_STATE: "US-NC",
  ND_STATE: "US-ND",
  OH_STATE: "US-OH",
  OK_STATE: "US-OK",
  OR_STATE: "US-OR",
  PA_STATE: "US-PA",
  RI_STATE: "US-RI",
  SC_STATE: "US-SC",
  SD_STATE: "US-SD",
  TN_STATE: "US-TN",
  TX_STATE: "US-TX",
  UT_STATE: "US-UT",
  VT_STATE: "US-VT",
  VA_STATE: "US-VA",
  WA_STATE: "US-WA",
  WV_STATE: "US-WV",
  WI_STATE: "US-WI",
  WY_STATE: "US-WY",
};

// Function to ensure the cache structure exists for a jurisdiction/year
function ensureCacheStructure(jur: string, year: number) {
    if (!HOLIDAYS[jur]) {
        HOLIDAYS[jur] = {};
    }
    if (!HOLIDAYS[jur][year]) {
        HOLIDAYS[jur][year] = new Set();
    }
}

// Accepts year, returns Promise<void>
export async function loadHolidays(year: number): Promise<void> {
  // Check if holidays for this year are already loaded for all relevant jurisdictions
  const jurisToLoad = Object.keys(jurisdictionToNagerCode).filter(j => !HOLIDAYS[j]?.[year]);
  if (jurisToLoad.length === 0) {
      console.log(`Holidays for ${year} already cached for all jurisdictions.`);
      return; // All needed data is cached
  }

  const fetchUrl = `https://date.nager.at/api/v3/PublicHolidays/${year}/US`;
  let allUsHolidays: NagerHoliday[] = [];

  try {
    console.log(`Fetching all US holidays for ${year} from ${fetchUrl}...`);
    const res = await fetch(fetchUrl);
    if (!res.ok) {
      console.error(`Failed to fetch US holidays for ${year}: ${res.status} ${res.statusText}`);
      // Initialize *only* the missing jurisdictions/year with empty sets
      jurisToLoad.forEach(j => {
          ensureCacheStructure(j, year); // Creates empty set
      });
      return;
    }
    allUsHolidays = (await res.json()) as NagerHoliday[];
    console.log(`Fetched ${allUsHolidays.length} total US holidays for ${year}.`);
  } catch (error) {
    console.error(`Error fetching US holidays for ${year}:`, error);
    // Initialize *only* the missing jurisdictions/year with empty sets
    jurisToLoad.forEach(j => {
        ensureCacheStructure(j, year); // Creates empty set
    });
    return;
  }

  // Populate HOLIDAYS cache for jurisdictions missing this year's data
  jurisToLoad.forEach(j => {
    ensureCacheStructure(j, year); // Make sure the structure exists
    const nagerCode = jurisdictionToNagerCode[j];
    if (!nagerCode) return;

    const stateHolidays = allUsHolidays.filter(h =>
      h.global === true || h.counties === null || (h.counties && h.counties.includes(nagerCode))
    );

    // Use the new cache structure HOLIDAYS[jurisdiction][year]
    HOLIDAYS[j][year] = new Set(stateHolidays.map(h => h.date));

    // Manually add known missing state holidays using the dynamic year
    // The Nager API data for 2025 was missing San Jacinto Day (April 21st) for Texas.
    // We add it manually here to ensure correct deadline calculations.
    if (j === "TX_STATE" && year >= 1837) { // San Jacinto Day established later
        const sanJacintoDate = `${year}-04-21`;
        HOLIDAYS[j][year].add(sanJacintoDate);
    }
    console.log(`Processed ${HOLIDAYS[j][year].size} holidays for ${j} (${nagerCode}) for ${year}`);
  });
}

// Accepts Date object, checks cache for the correct year
export const isHoliday = (jur: string, d: Date): boolean => {
  const year = d.getFullYear();
  const dateString = d.toISOString().slice(0, 10);
  // Check the specific year's cache
  return HOLIDAYS[jur]?.[year]?.has(dateString) ?? false; // Return false if year/jur not cached
};

// Helper function to check if holidays for a specific year are loaded
export const areHolidaysLoaded = (jur: string, year: number): boolean => {
    return !!HOLIDAYS[jur]?.[year];
};
