import express, { Request, Response } from 'express';
import { z } from 'zod';
import { loadRules, getRules, areRulesLoaded } from './loaders/ruleLoader.js';
import { loadHolidays, areHolidaysLoaded } from './loaders/holidayLoader.js';
import { calculateDeadlines } from './engine/calculator.js';

// Define ComputeRequest interface locally
interface ComputeRequest {
    jurisdiction: string;
    triggerCode: string;
    startDate: string; // YYYY-MM-DD string format
}

const app = express();
app.use(express.json());

const PORT = process.env.PORT || 3000;

// Define expected input schema for the calculate_deadlines tool
const CalculateDeadlinesParamsSchema = z.object({
    jurisdiction: z.string().min(1),
    triggerCode: z.string().min(1),
    startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
});

// Define the MCP request body schema
const McpRequestBodySchema = z.object({
    toolName: z.literal('calculate_deadlines'),
    params: CalculateDeadlinesParamsSchema,
});

// MCP Endpoint
app.post('/mcp/run', async (req: Request, res: Response): Promise<void> => {
    try {
        // Validate request body
        const validationResult = McpRequestBodySchema.safeParse(req.body);
        if (!validationResult.success) {
            res.status(400).json({ error: 'Invalid MCP request body', details: validationResult.error.errors });
            return;
        }

        const { toolName, params } = validationResult.data;
        const { jurisdiction, triggerCode, startDate } = params;

        // Parse start date and extract year
        const startDateObj = new Date(startDate);
        if (isNaN(startDateObj.getTime())) {
             res.status(400).json({ error: 'Invalid startDate format. Use YYYY-MM-DD.' });
             return;
        }
        const year = startDateObj.getFullYear();

        // Ensure rules and holidays are loaded for the required jurisdiction and year
        if (!areRulesLoaded(jurisdiction)) {
            console.log(`Rules for ${jurisdiction} not loaded. Loading...`);
            await loadRules(jurisdiction);
        }
        if (!areHolidaysLoaded(jurisdiction, year)) {
            console.log(`Holidays for ${jurisdiction} / ${year} not loaded. Loading...`);
            await loadHolidays(year);
        }

        // Now, explicitly check if rules were actually loaded before proceeding
        const rules = getRules(jurisdiction);
        if (!rules) {
             res.status(404).json({ error: `Rules not found or failed to load for jurisdiction: ${jurisdiction}` });
             return;
        }

        // Prepare the request object for the calculator function
        const computeRequest: ComputeRequest = {
            jurisdiction: jurisdiction,
            triggerCode: triggerCode,
            startDate: startDate
        };

        // Calculate deadlines using the single request object
        const deadlines = calculateDeadlines(computeRequest);

        // Return success response
        res.status(200).json({ result: deadlines });
        return;

    } catch (error: any) {
        console.error('Error processing MCP request:', error);
        res.status(500).json({ error: 'Internal server error', message: error.message });
        return;
    }
});

// Basic health check endpoint
app.get('/health', (req: Request, res: Response) => {
    res.status(200).send('OK');
});

app.listen(PORT, () => {
    console.log(`MCP Rules Engine server listening on port ${PORT}`);
    // Optional: Pre-load common rules/holidays on startup if desired
    // loadRules('TX_STATE').catch(console.error);
    // loadHolidays(new Date().getFullYear()).catch(console.error);
});
