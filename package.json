{"name": "pi-lawyer-mcp-rules", "version": "1.0.0", "type": "module", "description": "", "main": "index.js", "scripts": {"dev": "tsx watch src/server.ts", "test": "tsx scripts/generate-test-yaml.ts && NODE_ENV=development NODE_OPTIONS=--experimental-vm-modules jest --passWithNoTests", "build": "tsc -p .", "start": "node dist/server.js", "typecheck": "tsc --noEmit", "fetch:rules": "ts-node scripts/fetchRules.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/express": "^5.0.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "fastify": "^5.3.2", "fastify-swagger": "^5.2.0", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.1.91", "yaml": "^2.7.1", "zod": "^3.24.3"}, "devDependencies": {"@actions/core": "^1.10.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.2", "@types/node-fetch": "^2.6.12", "@vercel/node": "^3.0.0", "cross-fetch": "^3.1.4", "deep-diff": "^1.0.2", "jest": "^29.7.0", "js-yaml": "^4.1.0", "playwright": "^1.52.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.3"}}