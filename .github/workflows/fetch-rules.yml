name: Fetch & Update Court Rules

on:
  schedule:
    - cron: '0 2 * * *'
  workflow_dispatch:

jobs:
  fetch-rules:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install dependencies
        run: pnpm install

      - name: Run fetchRules script
        run: pnpm fetch:rules

      - name: Commit & Push changes
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "RuleBot"
          git diff --quiet || (git commit -am "chore: update rules" && git push)
