{"test_run": true, "test_limit": 5, "source_path": "/Users/<USER>/Documents/GitHub/pi-lawyer-mcp-rules/scripts/../test/fixtures/trcp-all-updated-with-amendments-effective-may-1-2020.pdf", "model_used": "gemini-2.5-pro-preview-06-05", "total_rules_tested": 5, "rules_extracted": 0, "rules_skipped": 5, "success_rate": "0/5 (0.0%)", "skipped_details": {"1.010": "Rule 1.010 not found in context by Gemini (finishReason: STOP)", "1.020": "Rule 1.020 not found in context by Gemini (finishReason: STOP)", "1.030": "Rule 1.030 not found in context by Gemini (finishReason: STOP)", "1.040": "Rule 1.040 not found in context by Gemini (finishReason: STOP)", "1.041": "Rule 1.041 not found in context by Gemini (finishReason: STOP)"}, "extracted_rules": []}