{"source_url": "https://www-media.floridabar.org/uploads/2025/01/Civil-Procedure-Rules-01-01-25-Corrected-Opinion.pdf", "model_used": "gemini-2.5-flash-preview-04-17", "total_rules_in_toc": 121, "rules_extracted": 1, "rules_skipped": 120, "skipped_details": {"1.010": "Rule 1.010 not found in context by Gemini (finishReason: STOP)", "1.020": "Rule 1.020 not found in context by Gemini (finishReason: STOP)", "1.030": "Rule 1.030 not found in context by Gemini (finishReason: STOP)", "1.041": "Rule 1.041 not found in context by Gemini (finishReason: STOP)", "1.050": "Rule 1.050 not found in context by Gemini (finishReason: STOP)", "1.060": "Rule 1.060 not found in context by Gemini (finishReason: STOP)", "1.061": "Rule 1.061 not found in context by Gemini (finishReason: STOP)", "1.070": "Rule 1.070 not found in context by Gemini (finishReason: STOP)", "1.071": "Rule 1.071 not found in context by Gemini (finishReason: STOP)", "1.080": "Rule 1.080 not found in context by Gemini (finishReason: STOP)", "1.090": "Rule 1.090 not found in context by Gemini (finishReason: STOP)", "1.100": "Rule 1.100 not found in context by Gemini (finishReason: STOP)", "1.110": "Rule 1.110 not found in context by Gemini (finishReason: STOP)", "1.115": "Rule 1.115 not found in context by Gemini (finishReason: STOP)", "1.120": "Rule 1.120 not found in context by Gemini (finishReason: STOP)", "1.130": "Rule 1.130 not found in context by Gemini (finishReason: STOP)", "1.140": "Rule 1.140 not found in context by Gemini (finishReason: STOP)", "1.150": "Rule 1.150 not found in context by Gemini (finishReason: STOP)", "1.160": "Rule 1.160 not found in context by Gemini (finishReason: STOP)", "1.170": "Rule 1.170 not found in context by Gemini (finishReason: STOP)", "1.180": "Rule 1.180 not found in context by Gemini (finishReason: STOP)", "1.190": "Rule 1.190 not found in context by Gemini (finishReason: STOP)", "1.200": "Rule 1.200 not found in context by Gemini (finishReason: STOP)", "1.201": "Rule 1.201 not found in context by Gemini (finishReason: STOP)", "1.202": "Rule 1.202 not found in context by Gemini (finishReason: STOP)", "1.210": "Rule 1.210 not found in context by Gemini (finishReason: STOP)", "1.220": "Rule 1.220 not found in context by Gemini (finishReason: STOP)", "1.221": "Rule 1.221 not found in context by Gemini (finishReason: STOP)", "1.222": "Rule 1.222 not found in context by Gemini (finishReason: STOP)", "1.230": "Rule 1.230 not found in context by Gemini (finishReason: STOP)", "1.240": "Rule 1.240 not found in context by Gemini (finishReason: STOP)", "1.250": "Rule 1.250 not found in context by Gemini (finishReason: STOP)", "1.260": "Rule 1.260 not found in context by Gemini (finishReason: STOP)", "1.270": "Rule 1.270 not found in context by Gemini (finishReason: STOP)", "1.280": "Rule 1.280 not found in context by Gemini (finishReason: STOP)", "1.285": "Rule 1.285 not found in context by Gemini (finishReason: STOP)", "1.290": "Rule 1.290 not found in context by Gemini (finishReason: STOP)", "1.300": "Rule 1.300 not found in context by Gemini (finishReason: STOP)", "1.310": "Rule 1.310 not found in context by Gemini (finishReason: STOP)", "1.320": "Rule 1.320 not found in context by Gemini (finishReason: STOP)", "1.330": "Rule 1.330 not found in context by Gemini (finishReason: STOP)", "1.340": "Rule 1.340 not found in context by Gemini (finishReason: STOP)", "1.350": "Rule 1.350 not found in context by Gemini (finishReason: STOP)", "1.351": "Rule 1.351 not found in context by Gemini (finishReason: STOP)", "1.360": "Rule 1.360 not found in context by Gemini (finishReason: STOP)", "1.370": "Rule 1.370 not found in context by Gemini (finishReason: STOP)", "1.380": "Rule 1.380 not found in context by Gemini (finishReason: STOP)", "1.390": "Rule 1.390 not found in context by Gemini (finishReason: STOP)", "1.410": "Rule 1.410 not found in context by Gemini (finishReason: STOP)", "1.420": "Rule 1.420 not found in context by Gemini (finishReason: STOP)", "1.430": "Rule 1.430 not found in context by Gemini (finishReason: STOP)", "1.431": "Rule 1.431 not found in context by Gemini (finishReason: STOP)", "1.440": "Rule 1.440 not found in context by Gemini (finishReason: STOP)", "1.442": "Rule 1.442 not found in context by Gemini (finishReason: STOP)", "1.450": "Rule 1.450 not found in context by Gemini (finishReason: STOP)", "1.451": "Rule 1.451 not found in context by Gemini (finishReason: STOP)", "1.452": "Rule 1.452 not found in context by Gemini (finishReason: STOP)", "1.453": "Rule 1.453 not found in context by Gemini (finishReason: STOP)", "1.455": "Rule 1.455 not found in context by Gemini (finishReason: STOP)", "1.460": "Rule 1.460 not found in context by Gemini (finishReason: STOP)", "1.470": "Rule 1.470 not found in context by Gemini (finishReason: STOP)", "1.480": "Rule 1.480 not found in context by Gemini (finishReason: STOP)", "1.481": "Rule 1.481 not found in context by Gemini (finishReason: STOP)", "1.490": "Rule 1.490 not found in context by Gemini (finishReason: STOP)", "1.491": "Rule 1.491 not found in context by Gemini (finishReason: STOP)", "1.500": "Rule 1.500 not found in context by Gemini (finishReason: STOP)", "1.510": "Rule 1.510 not found in context by Gemini (finishReason: STOP)", "1.520": "Rule 1.520 not found in context by Gemini (finishReason: STOP)", "1.525": "Rule 1.525 not found in context by Gemini (finishReason: STOP)", "1.530": "Rule 1.530 not found in context by Gemini (finishReason: STOP)", "1.535": "Rule 1.535 not found in context by Gemini (finishReason: STOP)", "1.540": "Rule 1.540 not found in context by Gemini (finishReason: STOP)", "1.545": "Rule 1.545 not found in context by Gemini (finishReason: STOP)", "1.550": "Rule 1.550 not found in context by Gemini (finishReason: STOP)", "1.560": "Rule 1.560 not found in context by Gemini (finishReason: STOP)", "1.570": "Rule 1.570 not found in context by Gemini (finishReason: STOP)", "1.580": "Rule 1.580 not found in context by Gemini (finishReason: STOP)", "1.590": "Rule 1.590 not found in context by Gemini (finishReason: STOP)", "1.600": "Rule 1.600 not found in context by Gemini (finishReason: STOP)", "1.610": "Rule 1.610 not found in context by Gemini (finishReason: STOP)", "1.611": "Rule 1.611 not found in context by Gemini (finishReason: STOP)", "1.620": "Rule 1.620 not found in context by Gemini (finishReason: STOP)", "1.625": "Rule 1.625 not found in context by Gemini (finishReason: STOP)", "1.630": "Rule 1.630 not found in context by Gemini (finishReason: STOP)", "1.650": "Rule 1.650 not found in context by Gemini (finishReason: STOP)", "1.700": "Rule 1.700 not found in context by Gemini (finishReason: STOP)", "1.710": "Rule 1.710 not found in context by Gemini (finishReason: STOP)", "1.720": "Rule 1.720 not found in context by Gemini (finishReason: STOP)", "1.730": "Rule 1.730 not found in context by Gemini (finishReason: STOP)", "1.750": "Rule 1.750 not found in context by Gemini (finishReason: STOP)", "1.800": "Rule 1.800 not found in context by Gemini (finishReason: STOP)", "1.810": "Rule 1.810 not found in context by Gemini (finishReason: STOP)", "1.820": "Rule 1.820 not found in context by Gemini (finishReason: STOP)", "1.830": "Rule 1.830 not found in context by Gemini (finishReason: STOP)", "1.840": "Rule 1.840 not found in context by Gemini (finishReason: STOP)", "1.900": "Rule 1.900 not found in context by Gemini (finishReason: STOP)", "1.902": "Rule 1.902 not found in context by Gemini (finishReason: STOP)", "1.907": "Rule 1.907 not found in context by Gemini (finishReason: STOP)", "1.908": "Rule 1.908 not found in context by Gemini (finishReason: STOP)", "1.910": "Rule 1.910 not found in context by Gemini (finishReason: STOP)", "1.911": "Rule 1.911 not found in context by Gemini (finishReason: STOP)", "1.912": "Rule 1.912 not found in context by Gemini (finishReason: STOP)", "1.913": "Rule 1.913 not found in context by Gemini (finishReason: STOP)", "1.916": "Rule 1.916 not found in context by Gemini (finishReason: STOP)", "1.918": "Rule 1.918 not found in context by Gemini (finishReason: STOP)", "1.919": "Rule 1.919 not found in context by Gemini (finishReason: STOP)", "1.921": "Rule 1.921 not found in context by Gemini (finishReason: STOP)", "1.922": "Rule 1.922 not found in context by Gemini (finishReason: STOP)", "1.923": "Rule 1.923 not found in context by Gemini (finishReason: STOP)", "1.933": "Rule 1.933 not found in context by Gemini (finishReason: STOP)", "1.960": "Rule 1.960 not found in context by Gemini (finishReason: STOP)", "1.976": "Rule 1.976 not found in context by Gemini (finishReason: STOP)", "1.982": "Rule 1.982 not found in context by Gemini (finishReason: STOP)", "1.983": "Rule 1.983 not found in context by Gemini (finishReason: STOP)", "1.984": "Rule 1.984 not found in context by Gemini (finishReason: STOP)", "1.988": "Rule 1.988 not found in context by Gemini (finishReason: STOP)", "1.996(a)": "Rule 1.996(a) not found in context by Gemini (finishReason: STOP)", "1.996(b)": "Rule 1.996(b) not found in context by Gemini (finishReason: STOP)", "1.997": "Rule 1.997 not found in context by Gemini (finishReason: STOP)", "1.999": "Rule 1.999 not found in context by Gemini (finishReason: STOP)"}, "extracted_rules": [{"code": "1.040", "title": "ONE FORM OF ACTION", "text": "RULE 1.040. ONE FORM OF ACTION\nThere shall be one form of action to be known as “civil action.”"}]}