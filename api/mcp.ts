import type { VercelRequest, VercelResponse } from '@vercel/node';
import { z } from 'zod';
import { calculateDeadlines } from '../src/engine/calculator.js';
import { loadRules } from '../src/loaders/ruleLoader.js';

// Define the Zod schema for the request body
const mcpRequestSchema = z.object({
  toolName: z.literal('calculate_deadlines'),
  params: z.object({
    jurisdiction: z.string(),
    triggerCode: z.string(),
    startDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
      message: "Invalid date format for startDate",
    }),
  })
});

// Define the type for the compute request parameters based on the schema
type ComputeRequest = z.infer<typeof mcpRequestSchema>['params'];

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  // Validate the request body against the schema
  const validationResult = mcpRequestSchema.safeParse(req.body);

  if (!validationResult.success) {
    return res.status(400).json({ 
      error: 'Invalid MCP request body',
      details: validationResult.error.errors
    });
  }
  
  try {
    const { jurisdiction, triggerCode, startDate } = validationResult.data.params;

    // Load rules for the jurisdiction (modifies global state, returns void)
    await loadRules(jurisdiction);

    // Construct the request object for calculateDeadlines
    const computeRequest: ComputeRequest = { 
      jurisdiction,
      triggerCode,
      startDate
    };

    // Call the *imported* calculateDeadlines with the single request object
    // Note: calculateDeadlines itself seems synchronous based on viewed code
    const deadlines = calculateDeadlines(computeRequest);
    
    return res.status(200).json(deadlines);
  } catch (error: any) {
    console.error('Error processing request:', error); // Keep logging for debugging

    // Check if the error message indicates a rule/trigger not found
    if (error instanceof Error && 
        (error.message.startsWith('Rules not found') || error.message.startsWith('Trigger code'))) {
      return res.status(404).json({ 
        error: 'Not Found',
        message: error.message 
      });
    }

    // Generic internal server error for other issues
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'An unexpected error occurred'
    });
  }
}
